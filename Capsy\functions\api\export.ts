export async function onRequestPost(ctx: any) {
  const { request, env } = ctx;

  // Auth: require valid session
  const cookie = request.headers.get('cookie') || '';
  const m = cookie.match(/(?:^|; )sid=([^;]+)/);
  const sid = m ? decodeURIComponent(m[1]) : null;
  if (!sid) return new Response(JSON.stringify({ error: 'unauthorized' }), { status: 401 });
  const sessRaw = await env.SESSIONS.get(`sid:${sid}`);
  if (!sessRaw) return new Response(JSON.stringify({ error: 'unauthorized' }), { status: 401 });
  const sess = JSON.parse(sessRaw);
  const userId = String(sess.userId || sess.id || 'unknown');

  let body: any = null;
  try {
    body = await request.json();
  } catch {
    return new Response(JSON.stringify({ error: 'invalid_json' }), { status: 400 });
  }

  const exporterURL = (env.EXPORT_SERVICE_URL || '').toString().trim();
  const exporterSecret = (env.EXPORT_SERVICE_SECRET || '').toString().trim();
  if (!exporterURL || !exporterSecret) {
    return new Response(JSON.stringify({ error: 'export_service_not_configured' }), { status: 500 });
  }

  const source: any = body?.source || {};
  const r2Key: string | undefined = source.r2Key || undefined;
  let fileURL: string | undefined = source.signedURL || source.fileURL || undefined;

  // If only r2Key provided, mint a short-lived signed URL via our /api/file
  if (!fileURL && r2Key) {
    const token = crypto.randomUUID();
    await env.SESSIONS.put(`dl:${token}`, r2Key, { expirationTtl: 60 * 120 });
    const origin = new URL(request.url).origin;
    fileURL = `${origin}/api/file?key=${encodeURIComponent(r2Key)}&token=${encodeURIComponent(token)}`;
  }
  if (!fileURL) {
    return new Response(JSON.stringify({ error: 'source_required', details: 'Provide source.r2Key or source.signedURL' }), { status: 400 });
  }

  // Build job payload for exporter
  const payload = {
    userId,
    source: { signedURL: fileURL },
    segments: Array.isArray(body?.segments) ? body.segments : [],
    captionStyle: body?.captionStyle || {},
    videoFX: body?.videoFX || {},
    watermark: !!body?.watermark,
    output: body?.output || {},
  };

  // Forward to exporter service
  const create = await fetch(`${exporterURL.replace(/\/$/, '')}/jobs`, {
    method: 'POST',
    headers: {
      'content-type': 'application/json',
      'authorization': `Bearer ${exporterSecret}`,
    },
    body: JSON.stringify(payload),
  });
  if (!create.ok) {
    const txt = await create.text().catch(()=>'');
    return new Response(JSON.stringify({ error: 'exporter_error', details: txt.slice(0, 500) }), { status: 502 });
  }
  const res = await create.json().catch(()=>null);
  const jobId = res?.jobId || res?.id || null;
  if (!jobId) {
    return new Response(JSON.stringify({ error: 'no_job_id' }), { status: 502 });
  }

  // Track job owner for optional access control or cleanup
  await env.SESSIONS.put(`expjob:${jobId}:user`, userId, { expirationTtl: 60 * 60 * 48 });

  return new Response(JSON.stringify({ jobId }), { headers: { 'content-type': 'application/json' } });
}


