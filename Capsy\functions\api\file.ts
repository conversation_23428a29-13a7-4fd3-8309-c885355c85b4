function bad(status: number, msg: string) {
  return new Response(JSON.stringify({ error: msg }), { status, headers: { 'content-type': 'application/json' } });
}

export async function onRequestGet(ctx: any) {
  const { request, env } = ctx;
  const url = new URL(request.url);
  const key = url.searchParams.get('key');
  const token = url.searchParams.get('token');
  if (!key || !token) return bad(400, 'missing_params');

  // Validate ephemeral token
  const stored = await env.SESSIONS.get(`dl:${token}`);
  if (!stored || stored !== key) return bad(403, 'forbidden');

  const bucket: R2Bucket | undefined = (env as any).bucket || (env as any).R2 || (env as any).BUCKET;
  if (!bucket) return bad(500, 'r2_not_configured');

  const obj = await bucket.get(key);
  if (!obj) return bad(404, 'not_found');

  const headers = new Headers();
  const ct = obj.httpMetadata?.contentType || 'application/octet-stream';
  headers.set('content-type', ct);
  // Make sure the file is downloadable by providers
  headers.set('cache-control', 'public, max-age=3600');

  return new Response(obj.body, { headers });
}

// Public read-only for assets like watermark/logo. No token required.
export async function onRequestGetPublic(ctx: any) {
  const { request, env } = ctx;
  const url = new URL(request.url);
  const key = url.searchParams.get('key');
  if (!key) return bad(400, 'missing_params');
  const bucket: R2Bucket | undefined = (env as any).bucket || (env as any).R2 || (env as any).BUCKET;
  if (!bucket) return bad(500, 'r2_not_configured');
  const obj = await bucket.get(key);
  if (!obj) return bad(404, 'not_found');
  const headers = new Headers();
  const ct = obj.httpMetadata?.contentType || 'application/octet-stream';
  headers.set('content-type', ct);
  headers.set('cache-control', 'public, max-age=86400');
  return new Response(obj.body, { headers });
}


