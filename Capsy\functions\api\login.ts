export async function onRequestPost(ctx: any) {
  try {
    const { request, env } = ctx;
    if (!request.headers.get("content-type")?.includes("application/json")) {
      return new Response(JSON.stringify({ error: "application/json required" }), { status: 415 });
    }
    const body = await request.json().catch(() => ({}));
    const email = String(body.email || "").toLowerCase().trim();
    if (!email || !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      return new Response(JSON.stringify({ error: "valid_email_required" }), { status: 400, headers: { 'content-type': 'application/json' } });
    }

    const db = env.DB as D1Database;
    let user = await db
      .prepare("SELECT id, email FROM users WHERE email = ?")
      .bind(email)
      .first<{ id: number; email: string }>();
    if (!user) {
      await db.prepare("INSERT INTO users (email, provider) VALUES (?, ?)").bind(email, "email").run();
      user = await db.prepare("SELECT id, email FROM users WHERE email = ?").bind(email).first();
    }

    // Create a one-time magic link token and send email
    const token = crypto.randomUUID();
    const ttl = 15 * 60; // 15 minutes
    await env.SESSIONS.put(`magic:${token}`, JSON.stringify({ email }), { expirationTtl: ttl });

    const url = new URL(request.url);
    const origin = `${url.protocol}//${url.host}`;
    const verifyUrl = `${origin}/api/verify?token=${encodeURIComponent(token)}`;

    // Try to send via Resend if configured, otherwise just succeed (no-op)
    const fromEmail = env.EMAIL_FROM as string | undefined;
    const resendKey = env.RESEND_API_KEY as string | undefined;
    if (resendKey && fromEmail) {
      const subject = 'Your Capsy sign-in link';
      const html = `<p>Click to sign in to Capsy:</p><p><a href="${verifyUrl}">${verifyUrl}</a></p><p>This link expires in 15 minutes.</p>`;
      try {
        await fetch('https://api.resend.com/emails', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${resendKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ from: fromEmail, to: email, subject, html }),
        });
      } catch (_) {
        // Ignore email failures to avoid leaking token; client will still see generic success
      }
    }

    return new Response(JSON.stringify({ ok: true }), { headers: { 'content-type': 'application/json' } });
  } catch (e) {
    return new Response(JSON.stringify({ error: "login_failed" }), { status: 500, headers: { 'content-type': 'application/json' } });
  }
}


