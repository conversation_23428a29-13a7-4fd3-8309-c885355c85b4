gcloud run deploy capsy-exporter --source . --region europe-west1 --platform managed --allow-unauthenticated --set-env-vars "EXPORT_SERVICE_SECRET=randomsecretstring123,R2_ACCOUNT_ID=42ceae35179b19aa91be51c7b3e5dc4a,R2_ACCESS_KEY_ID=1c431c103929ce1e5faded7e3ab7b36c,R2_SECRET_ACCESS_KEY=69b40de2a748eeded4689bcba2c447cb40dd6ef0f3ea4fd81a7295b39519a379,R2_BUCKET=bucket"



gcloud run deploy capsy-exporter `
  --image europe-west1-docker.pkg.dev/capsye-renderer/capsy-exporter/capsy-exporter:gpu `
  --region europe-west1 `
  --allow-unauthenticated `
  --gpu 1 `
  --gpu-type nvidia-l4 `
  --cpu 4 `
  --memory 16Gi `
  --concurrency 1