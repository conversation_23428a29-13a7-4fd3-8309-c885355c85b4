export async function onRequestGet(ctx: any) {
  const { request, env } = ctx;
  const url = new URL(request.url);
  const provider = url.searchParams.get('provider') || 'assemblyai';
  const jobId = url.searchParams.get('jobId');
  if (!jobId) return new Response(JSON.stringify({ error: 'jobId_required' }), { status: 400 });

  if (provider === 'assemblyai') {
    const AAI = env.ASSEMBLYAI_API_KEY as string;
    if (!AAI) return new Response(JSON.stringify({ error: 'assemblyai_not_configured' }), { status: 500 });
    const r = await fetch(`https://api.assemblyai.com/v2/transcript/${encodeURIComponent(jobId)}`, {
      headers: { 'authorization': AAI }
    });
    if (!r.ok) {
      const err = await r.text().catch(()=> '');
      return new Response(JSON.stringify({ error: 'status_failed', details: err.slice(0,500) }), { status: 500 });
    }
    const data = await r.json();
    const stripPunct = (txt: string) => (txt || '').replace(/[\.,!?;:\-–—"'“”‘’\(\)\[\]…]/g, '').replace(/\s+/g,' ').trim();
    if (data.status === 'completed') {
      // Map words/utterances to segments
      const segments = Array.isArray(data.utterances) && data.utterances.length ?
        data.utterances.map((u: any) => ({ start: u.start/1000, end: u.end/1000, text: stripPunct(u.text||''), userAdded: false })) :
        (Array.isArray(data.words) ? coalesceWordsToSegments(data.words).map(s => ({...s, text: stripPunct(s.text)})) : []);
      return new Response(JSON.stringify({ status: 'completed', segments }), { headers: { 'content-type': 'application/json' } });
    }
    if (data.status === 'error') {
      return new Response(JSON.stringify({ status: 'error', error: data.error || 'unknown' }), { status: 500 });
    }
    return new Response(JSON.stringify({ status: data.status }), { headers: { 'content-type': 'application/json' } });
  }

  return new Response(JSON.stringify({ error: 'unsupported_provider' }), { status: 400 });
}

function coalesceWordsToSegments(words: any[]) {
  // Simple fallback: group words into ~5 second chunks
  const segs: any[] = [];
  let cur: any = null;
  for (const w of words || []) {
    const start = (w.start || 0) / 1000;
    const end = (w.end || w.start || 0) / 1000;
    const text = String(w.text || '').trim();
    if (!cur) cur = { start, end, text };
    else if ((end - cur.start) <= 5) { cur.end = end; cur.text += (cur.text ? ' ' : '') + text; }
    else { segs.push({ ...cur, userAdded: false }); cur = { start, end, text }; }
  }
  if (cur) segs.push({ ...cur, userAdded: false });
  return segs;
}


