export async function onRequestPost(ctx: any) {
  const { request, env } = ctx;
  // Require authenticated session (protect provider keys)
  const cookie = request.headers.get('cookie') || '';
  const m = cookie.match(/(?:^|; )sid=([^;]+)/);
  const sid = m ? decodeURIComponent(m[1]) : null;
  if (!sid || !(await env.SESSIONS.get(`sid:${sid}`))) {
    return new Response(JSON.stringify({ error: 'unauthorized' }), { status: 401 });
  }

  const ctype = request.headers.get("content-type") || "";
  const form = await request.formData();

  const language = (form.get("language") as string) || undefined;
  const translate = (form.get("translate") as string) === "true";

  let file: File | null = null;
  let r2Key: string | null = null;
  if (ctype.includes("multipart/form-data")) {
    file = form.get("file") as File | null;
  }
  r2Key = (form.get("r2Key") as string) || null;

  // Load from R2 if key provided; otherwise expect a file in the form
  if (!file && r2Key) {
    const bucket = (env as any).bucket || (env as any).R2 || (env as any).BUCKET;
    if (!bucket) {
      return new Response(JSON.stringify({ error: "r2_not_configured" }), { status: 500 });
    }
    const obj = await bucket.get(r2Key);
    if (!obj) {
      return new Response(JSON.stringify({ error: "not_found", key: r2Key }), { status: 404 });
    }
    const ct = obj.httpMetadata?.contentType || "application/octet-stream";
    const arr = await obj.arrayBuffer();
    file = new File([arr], r2Key.split("/").pop() || "audio.bin", { type: ct });
  }

  // Always use AssemblyAI (async URL-based provider path)
    const AAI = env.ASSEMBLYAI_API_KEY as string;
    if (!AAI) return new Response(JSON.stringify({ error: 'assemblyai_not_configured' }), { status: 500 });

    // If we received a file, ensure it’s uploaded and we have a URL
    let fileURL = (form.get('fileURL') as string) || '';
    if (!fileURL) {
      if (!r2Key) return new Response(JSON.stringify({ error: 'fileURL_or_r2Key_required' }), { status: 400 });
      const token = crypto.randomUUID();
      await env.SESSIONS.put(`dl:${token}`, r2Key, { expirationTtl: 60 * 30 });
      const origin = new URL(request.url).origin;
      fileURL = `${origin}/api/file?key=${encodeURIComponent(r2Key)}&token=${encodeURIComponent(token)}`;
    }

    // Create transcription job
    const payload: any = {
      audio_url: fileURL,
      punctuate: false,
      format_text: true,
      speaker_labels: false,
      auto_highlights: false,
      dual_channel: false,
      word_boost: [],
      redact_pii: false,
      auto_chapters: false,
      // request detailed words/utterances
      disfluencies: false,
    };
    if (language) {
      payload.language_code = language;
    } else {
      // Enable Automatic Language Detection when language is not provided
      payload.language_detection = true;
      payload.language_confidence_threshold = 0.4; // proceed even with moderate confidence
    }
    const create = await fetch('https://api.assemblyai.com/v2/transcript', {
      method: 'POST',
      headers: { 'authorization': AAI, 'content-type': 'application/json' },
      body: JSON.stringify(payload)
    });
    if (!create.ok) {
      const err = await create.text().catch(()=> '');
      return new Response(JSON.stringify({ error: 'create_failed', details: err.slice(0,500) }), { status: 500 });
    }
    const job = await create.json();
    // Remember which R2 object belongs to this job so we can clean it up when done
    if (r2Key) {
      // expire in 2 days just in case status polling stops
      await env.SESSIONS.put(`job:${job.id}:r2key`, r2Key, { expirationTtl: 60 * 60 * 48 });
    }
    return new Response(JSON.stringify({ jobId: job.id, provider: 'assemblyai' }), { headers: { 'content-type': 'application/json' } });
}


