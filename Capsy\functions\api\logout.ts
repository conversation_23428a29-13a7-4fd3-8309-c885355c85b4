function getCookie(name: string, cookie: string | null) {
  const m = cookie?.match(new RegExp(`(?:^|; )${name}=([^;]+)`));
  return m ? decodeURIComponent(m[1]) : null;
}

export async function onRequestPost(ctx: any) {
  const { request, env } = ctx;
  const sid = getCookie("sid", request.headers.get("cookie"));
  if (sid) await env.SESSIONS.delete(`sid:${sid}`);

  const expired = ["sid=", "Path=/", "HttpOnly", "Secure", "SameSite=Lax", "Max-Age=0"].join("; ");
  return new Response(JSON.stringify({ ok: true }), {
    headers: { "content-type": "application/json", "set-cookie": expired },
  });
}


