export async function onRequestGet(ctx: any) {
  const { env } = ctx;
  const maxUploadMb = Number(env.MAX_UPLOAD_MB || 200);
  const maxDailyUploads = Number(env.MAX_DAILY_UPLOADS || 3);
  const maxDailyMb = Number(env.MAX_DAILY_MB || 500);
  return new Response(
    JSON.stringify({ maxUploadMb, maxDailyUploads, maxDailyMb }),
    { headers: { 'content-type': 'application/json', 'cache-control': 'public, max-age=300' } }
  );
}


