<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>Capsy — AI Captions</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <!-- Tailwind Play CDN (fine for prototyping) -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&family=Poppins:wght@400;600;800&family=Montserrat:wght@400;600;800&family=Oswald:wght@400;600;700&family=Bebas+Neue&family=Anton&family=Lexend:wght@400;600;800&family=Outfit:wght@400;600;800&family=Barlow:wght@400;600;800&family=Archivo+Black&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
</head>
<body class="text-neutral-100">
  <div id="root"></div>

  <!-- React via CDN (for a quick single-file demo) -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

  <script type="text/babel">
    const {useState, useRef, useMemo, useEffect} = React;

    // ---------- Helpers ----------
    function formatTime(t) {
      if (!Number.isFinite(t)) return "0:00.000";
      const ms = Math.floor((t % 1) * 1000).toString().padStart(3,'0');
      const sTot = Math.floor(t);
      const m = Math.floor(sTot/60);
      const s = (sTot%60).toString().padStart(2,'0');
      return `${m}:${s}.${ms}`;
    }
    function parseTimeToSeconds(val) {
      // Accepts "ss", "ss.mmm", "m:ss", "m:ss.mmm", "h:mm:ss", "h:mm:ss.mmm" and floats
      if (val == null) return NaN;
      const raw = String(val).trim().replace(/,/g, '.');
      if (!raw) return NaN;
      if (/^\d+(?:\.\d+)?$/.test(raw)) return Number(raw);
      const parts = raw.split(":");
      if (parts.length === 2) {
        const m = Number(parts[0]) || 0;
        const s = Number(parts[1]) || 0;
        return m * 60 + s;
      }
      if (parts.length === 3) {
        const h = Number(parts[0]) || 0;
        const m = Number(parts[1]) || 0;
        const s = Number(parts[2]) || 0;
        return h * 3600 + m * 60 + s;
      }
      return Number(raw);
    }

    // SRT / VTT
    function segmentsToSRT(segments) {
      const toStamp = (t) => {
        const h = String(Math.floor(t/3600)).padStart(2,"0");
        const m = String(Math.floor((t%3600)/60)).padStart(2,"0");
        const s = String(Math.floor(t%60)).padStart(2,"0");
        const ms = String(Math.floor((t%1)*1000)).padStart(3,"0");
        return `${h}:${m}:${s},${ms}`;
      };
      return segments.map((seg, i) =>
        `${i+1}\n${toStamp(seg.start)} --> ${toStamp(seg.end)}\n${seg.text || ""}\n`
      ).join("\n");
    }
    function segmentsToVTT(segments) {
      const toStamp = (t) => {
        const h = String(Math.floor(t/3600)).padStart(2,"0");
        const m = String(Math.floor((t%3600)/60)).padStart(2,"0");
        const s = String(Math.floor(t%60)).padStart(2,"0");
        const ms = String(Math.floor((t%1)*1000)).padStart(3,"0");
        return `${h}:${m}:${s}.${ms}`;
      };
      return "WEBVTT\n\n" + segments.map(seg =>
        `${toStamp(seg.start)} --> ${toStamp(seg.end)}\n${seg.text || ""}\n`
      ).join("\n");
    }
    function downloadText(filename, text) {
      const blob = new Blob([text], {type:'text/plain;charset=utf-8'});
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url; a.download = filename; a.click();
      setTimeout(()=>URL.revokeObjectURL(url), 1000);
    }

    // Estimate source FPS using requestVideoFrameCallback when available
    async function estimateVideoFps(videoEl, sampleFrames = 18) {
      try {
        if (!videoEl || typeof videoEl.requestVideoFrameCallback !== 'function') return null;
        const prevPaused = videoEl.paused;
        const prevTime = videoEl.currentTime;
        const timestamps = [];
        let resolveDone;
        const done = new Promise(r => resolveDone = r);
        let count = 0;
        const cb = (_now, meta) => {
          if (count > 0) timestamps.push(meta.mediaTime);
          count++;
          if (count >= sampleFrames) resolveDone();
          else videoEl.requestVideoFrameCallback(cb);
        };
        await videoEl.play().catch(()=>{});
        videoEl.requestVideoFrameCallback(cb);
        await done;
        videoEl.pause();
        videoEl.currentTime = prevTime;
        if (timestamps.length < 2) return null;
        const diffs = [];
        for (let i=1;i<timestamps.length;i++) diffs.push(timestamps[i]-timestamps[i-1]);
        const avg = diffs.reduce((a,b)=>a+b,0)/diffs.length;
        return avg > 0 ? 1/avg : null;
      } catch { return null; }
    }

    // Minimal SRT/VTT parsers
    function parseSRTorVTT(content) {
      // Remove WEBVTT header if exists
      content = content.replace(/^WEBVTT[^\n]*\n+/i, "");
      // Split blocks
      const blocks = content.split(/\n\s*\n/).map(b => b.trim()).filter(Boolean);
      const segments = [];
      for (const b of blocks) {
        const lines = b.split("\n").filter(Boolean);
        // possible index line first
        let i = 0;
        if (/^\d+$/.test(lines[0])) i = 1;
        const timeLine = lines[i++] || "";
        const m = timeLine.match(/(\d{2}:\d{2}:\d{2}[,.]\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2}[,.]\d{3})/);
        if (!m) continue;
        const toSec = (ts) => {
          const [h,m,sms] = ts.replace(",",".").split(":");
          const s = parseFloat(sms);
          return (+h)*3600 + (+m)*60 + s;
        };
        const text = lines.slice(i).join("\n");
        segments.push({ start: toSec(m[1]), end: toSec(m[2]), text });
      }
      return segments.sort((a,b)=>a.start-b.start);
    }

    // Fake transcriber (replace with your API)
    async function fakeTranscribe(file, duration) {
      // Creates 2.5s chunks with placeholder text to demonstrate the flow
      await new Promise(r => setTimeout(r, 1000));
      const segs = [];
      const step = 2.5;
      let t = 0, i=1;
      while (t < duration) {
        const end = Math.min(duration, t + step);
        segs.push({ start: t, end, text: `Caption segment ${i}` });
        t = end; i++;
      }
      return segs;
    }

    // Real transcription via backend API
    async function extractAudioBlobFromVideo(videoEl) {
      // Prefer WebAudio capture (consistent even when element is muted),
      // fall back to captureStream only if MediaElementSource is unavailable.
      const mimeCandidates = ["audio/webm;codecs=opus", "audio/webm", "audio/mp4", "audio/ogg"];
      const pickMime = () => mimeCandidates.find(m => window.MediaRecorder && MediaRecorder.isTypeSupported(m)) || "";
      const prevT = videoEl.currentTime;
      const prevMuted = videoEl.muted;
      videoEl.muted = true;
      videoEl.currentTime = 0;

      let recorder;
      let chunks = [];
      let stopPromise;

      function startRecorder(stream) {
        const mime = pickMime();
        const rec = mime ? new MediaRecorder(stream, { mimeType: mime }) : new MediaRecorder(stream);
        chunks = [];
        stopPromise = new Promise(res => (rec.onstop = res));
        rec.ondataavailable = e => e.data && e.data.size && chunks.push(e.data);
        rec.start(250);
        return rec;
      }

      const AC = window.AudioContext || window.webkitAudioContext;
      try {
        if (AC) {
          const ctx = new AC();
          try {
            const source = ctx.createMediaElementSource(videoEl);
            const dest = ctx.createMediaStreamDestination();
            // Route to recorder only (avoid echo). Element output stays muted during capture.
            source.connect(dest);
            recorder = startRecorder(dest.stream);
            stopPromise.finally(() => { try { ctx.close(); } catch(e){} });
          } catch (e) {
            try { ctx.close(); } catch(_){}
            throw e;
          }
        } else if (typeof videoEl.captureStream === 'function') {
          const stream = videoEl.captureStream();
          const audioTracks = stream.getAudioTracks();
          if (!audioTracks || audioTracks.length === 0) throw new Error("No audio track in video");
          recorder = startRecorder(new MediaStream([audioTracks[0]]));
        } else {
          throw new Error('No supported audio capture method');
        }

        await videoEl.play().catch(()=>{});
        await new Promise(resolve => {
          const onEnd = () => { try { recorder.stop(); } catch(_){} videoEl.removeEventListener("ended", onEnd); resolve(); };
          videoEl.addEventListener("ended", onEnd);
        });
        await stopPromise;
        const type = pickMime() || "audio/webm";
        return new Blob(chunks, { type });
      } finally {
        videoEl.currentTime = prevT;
        videoEl.muted = prevMuted;
      }
    }
    async function realTranscribeViaAPI(videoEl, opts = {}) {
      // Deprecated: no longer record audio in the browser.
      // Use server-side upload + transcription via R2.
      throw new Error('Client-side audio extraction disabled');
    }

      // Directly send the uploaded file (video or audio) to the backend for transcription.
    async function transcribeFileViaAPI(file, opts = {}) {
      // Require a real server session (sid cookie). The client Auth mock is not sufficient.
      const me = await fetch('/api/me', { credentials: 'include' }).then(r=>r.json()).catch(()=>({user:null}));
      if (!me?.user) { throw new Error('Please log in with Email to create a server session.'); }

      // Fetch server limits and pre-check file size to avoid network-level 413 errors
      try {
        const lim = await fetch('/api/limits', { credentials: 'include' }).then(r => r.ok ? r.json() : Promise.reject());
        const maxMb = Number(lim?.maxUploadMb || 0);
        if (maxMb > 0 && file && file.size > maxMb * 1024 * 1024) {
          throw new Error(`File is too large. Max ${maxMb} MB.`);
        }
      } catch(_) {}

      // 1) Upload to server (R2)
      const up = new FormData();
      up.append('file', file);
      const upResp = await fetch('/api/upload', { method: 'POST', body: up, credentials: 'include' });
      if (!upResp.ok) {
        let message = 'Upload failed';
        try {
          const ct = upResp.headers.get('content-type') || '';
          if (ct.includes('application/json')) {
            const j = await upResp.json();
            if (upResp.status === 413 && (j?.error === 'file_too_large' || j?.error === 'payload_too_large')) {
              throw new Error(`File is too large. Max ${j?.maxMb ?? ''}${j?.maxMb ? ' MB' : ''}.`);
            }
            if (upResp.status === 429 && j?.error === 'daily_upload_limit_reached') {
              throw new Error('Daily upload limit reached. Please try again tomorrow.');
            }
            if (upResp.status === 429 && j?.error === 'daily_bytes_limit_reached') {
              const used = (j?.usedMb && j?.limitMb) ? ` (${j.usedMb}/${j.limitMb} MB)` : '';
              throw new Error(`Daily data limit reached${used}. Please try again tomorrow.`);
            }
            message = j?.error ? `Upload failed: ${j.error}` : message;
          } else {
            const text = await upResp.text();
            if (upResp.status === 413 && /payload_too_large|file_too_large/i.test(text)) {
              throw new Error('File is too large.');
            }
            message = `Upload failed${text ? `: ${text.slice(0,200)}` : ''}`;
          }
        } catch(_) {}
        throw new Error(message);
      }
      const { key, fileURL } = await upResp.json();
      try { window.__LAST_UPLOAD_KEY__ = key; } catch(_) {}

      // Always use AssemblyAI async job
      const f = new FormData();
      f.append('provider', 'assemblyai');
      f.append('r2Key', key);
      if (opts.language) f.append('language', String(opts.language));
      const create = await fetch('/api/transcribe', { method: 'POST', body: f, credentials: 'include' });
      if (!create.ok) {
        let details = ''; try { details = await create.text(); } catch {}
        throw new Error(`Create job failed${details ? `: ${details.slice(0,200)}` : ''}`);
      }
      const { jobId } = await create.json();
      // Poll status until completed
      const start = Date.now();
      while (true) {
        await new Promise(r => setTimeout(r, 2000));
        const st = await fetch(`/api/transcribe_status?provider=assemblyai&jobId=${encodeURIComponent(jobId)}`, { credentials: 'include' });
        if (!st.ok) {
          let details = ''; try { details = await st.text(); } catch {}
          throw new Error(`Status failed${details ? `: ${details.slice(0,200)}` : ''}`);
        }
        const data = await st.json();
        if (data.status === 'completed') return Array.isArray(data.segments) ? data.segments : [];
        if (data.status === 'error') throw new Error('Transcription failed (provider error).');
        if (Date.now() - start > 15 * 60 * 1000) throw new Error('Transcription timed out.');
      }
    }

    // ---------- Auth + Token model (client-only mock; replace with real backend later) ----------
    const Auth = (()=>{
      const USER_KEY = 'capsy:user';
      const tokenKey = (uid)=>`capsy:tokens:${uid}`;
      const subKey = (uid)=>`capsy:sub:${uid}`;
      const PLAN = { free: { allowance: 100, watermark: true }, pro: { allowance: 1000, watermark: false } };
      function getUser(){ try { return JSON.parse(localStorage.getItem(USER_KEY) || 'null'); } catch { return null; } }
      function login(email){ const user = { id: (email||'').toLowerCase().trim(), email: (email||'').trim(), provider: 'email' }; localStorage.setItem(USER_KEY, JSON.stringify(user)); ensureBucket(user.id); return user; }
      function googleLogin(){ const rnd = Math.random().toString(36).slice(2,8); const email = `user_${rnd}@gmail.com`; const user = { id: email, email, provider: 'google' }; localStorage.setItem(USER_KEY, JSON.stringify(user)); ensureBucket(user.id); return user; }
      function logout(){ localStorage.removeItem(USER_KEY); }
      function isSubscribed(uid){ return !!JSON.parse(localStorage.getItem(subKey(uid)) || 'false'); }
      function setSubscribed(uid, on){ localStorage.setItem(subKey(uid), JSON.stringify(!!on)); // adjust bucket to plan allowance bounds
        const b = getTokenBucket(uid); const allowance = getPlan(uid).allowance; const next = { ...b, allowance };
        if (on) next.tokensRemaining = Math.max(b.tokensRemaining, allowance); else next.tokensRemaining = Math.min(b.tokensRemaining, allowance);
        localStorage.setItem(tokenKey(uid), JSON.stringify(next));
      }
      function getPlan(uid){ return isSubscribed(uid) ? PLAN.pro : PLAN.free; }
      function getMonthStartMs(nowMs){ const d = new Date(nowMs); return Date.UTC(d.getUTCFullYear(), d.getUTCMonth(), 1, 0,0,0,0); }
      function ensureBucket(uid){ const now = Date.now(); const monthStart = getMonthStartMs(now); const plan = getPlan(uid); let data; try { data = JSON.parse(localStorage.getItem(tokenKey(uid)) || 'null'); } catch { data = null; }
        if (!data || data.monthStart !== monthStart) { data = { monthStart, tokensRemaining: plan.allowance, allowance: plan.allowance }; localStorage.setItem(tokenKey(uid), JSON.stringify(data)); }
        else if (data.allowance !== plan.allowance) { data.allowance = plan.allowance; data.tokensRemaining = Math.min(data.tokensRemaining, plan.allowance); localStorage.setItem(tokenKey(uid), JSON.stringify(data)); }
        return data;
      }
      function getTokenBucket(uid){ const b = ensureBucket(uid); const resetDate = new Date(b.monthStart); resetDate.setUTCMonth(resetDate.getUTCMonth()+1); return { ...b, resetISO: resetDate.toISOString() }; }
      function consumeTokens(uid, amount){ if (amount <= 0) return true; const b = getTokenBucket(uid); if (b.tokensRemaining < amount) return false; const next = { ...b, tokensRemaining: b.tokensRemaining - amount }; localStorage.setItem(tokenKey(uid), JSON.stringify(next)); return true; }
      function getEntitlement(uid){ const subscribed = isSubscribed(uid); const bucket = getTokenBucket(uid); const plan = getPlan(uid); return { subscribed, tokensRemaining: bucket.tokensRemaining, allowance: bucket.allowance, resetISO: bucket.resetISO, watermark: plan.watermark } }
      return { getUser, login, googleLogin, logout, isSubscribed, setSubscribed, getPlan, getTokenBucket, consumeTokens, getEntitlement };
    })();
    window.__APP_AUTH__ = Auth;

    // Configure R2-hosted logos
    window.__CAPSY_LOGO_KEY__ = 'assets/capsy-logo.png';
    window.__CAPSY_LOGO_MINI_KEY__ = 'assets/capsy-logo-mini.png';
    const logoVersion = String(Date.now()).slice(0,10);
    const __CAPSY_LOGO_KEY__ = window.__CAPSY_LOGO_KEY__ || null;
    const __CAPSY_LOGO_MINI_KEY__ = window.__CAPSY_LOGO_MINI_KEY__ || null;
    const heroLogoURL = __CAPSY_LOGO_KEY__ ? (`/api/file_public?key=${encodeURIComponent(__CAPSY_LOGO_KEY__)}&v=${logoVersion}`) : null;
    const miniLogoURL = __CAPSY_LOGO_MINI_KEY__ ? (`/api/file_public?key=${encodeURIComponent(__CAPSY_LOGO_MINI_KEY__)}&v=${logoVersion}`) : null;
    window.__CAPSY_LOGO_OPACITY__ = 0.4; // 0..1
    window.__CAPSY_LOGO_KEY__ = window.__CAPSY_LOGO_KEY__ || null;
    window.__CAPSY_LOGO_MINI_KEY__ = window.__CAPSY_LOGO_MINI_KEY__ || null;
    window.__CAPSY_LOGO_OPACITY__ = typeof window.__CAPSY_LOGO_OPACITY__ === 'number' ? window.__CAPSY_LOGO_OPACITY__ : 0.4;

    // Ensure client Auth mirrors server session on load (after email verify / Google OAuth)
    async function syncClientFromServer(){
      try {
        const me = await fetch('/api/me', { credentials: 'include' }).then(r=>r.json()).catch(()=>({user:null}));
        const local = Auth.getUser();
        if (me?.user && (!local || local.email !== me.user.email)) {
          Auth.login(me.user.email);
        }
      } catch(_) {}
    }
    // run on load and after OAuth redirect
    syncClientFromServer();
    window.addEventListener('pageshow', syncClientFromServer);

    // ---------- Router + Pages ----------
    function useHashRoute(){
      const [route, setRoute] = React.useState(()=> (location.hash.replace(/^#\//,'') || ''));
      useEffect(()=>{ const onHash = ()=> setRoute(location.hash.replace(/^#\//,'') || ''); window.addEventListener('hashchange', onHash); return ()=>window.removeEventListener('hashchange', onHash); }, []);
      return route;
    }
    function LandingPage(){
      const user = Auth.getUser();
      return (
        <div className="min-h-screen">
          <header className="sticky top-0 z-10 border-b theme-border site-header backdrop-blur-xl">
            <div className="mx-auto max-w-7xl px-4 py-3 flex items-center justify-between">
              <div className="flex items-center gap-3">
                 {miniLogoURL ? (
                  <a href="#/" className="inline-flex items-center">
                    <img src={miniLogoURL} alt="Capsy" className="h-8 w-auto" onError={(e)=>{ e.currentTarget.onerror=null; e.currentTarget.src='/assets/capsy-logo-mini.png'; }}/>
                  </a>
                ) : (
                  <div className="font-semibold tracking-wide theme-strong">Capsy</div>
                )}
                <a href="#/pricing" className="ml-4 theme-muted hover:theme-strong text-sm">Pricing</a>
                <a href="#/editor" className="ml-2 text-neutral-300 hover:text-white text-sm">Editor</a>
              </div>
              <div className="flex items-center gap-2">
                 {user ? (
                  <>
                    <div className="hidden sm:flex items-center gap-2 px-3 py-1.5 rounded-xl btn-outline text-sm">
                      <span className="font-mono truncate max-w-[16ch]" title={user.email}>{user.email}</span>
                      <span className="text-neutral-400">·</span>
                      <span title="Tokens remaining">{Auth.getEntitlement(user.id).tokensRemaining} tokens</span>
                      <span className="text-neutral-400">·</span>
                      <span>{Auth.getEntitlement(user.id).subscribed ? 'Pro' : 'Free'}</span>
                    </div>
                    <a href="#/account" className="px-3 py-1.5 rounded-xl btn-outline text-sm">Account</a>
                  </>
                ) : (
                  <a href="#/login" className="px-3 py-1.5 rounded-xl btn-accent text-sm">Log in</a>
                )}
              </div>
            </div>
          </header>
          <main className="mx-auto max-w-7xl p-6">
            <section className="relative overflow-hidden rounded-3xl border border-indigo-500/20 bg-gradient-to-br from-[rgba(34,34,34,0.75)] to-[rgba(215,72,0,0.08)] p-10 text-center">
              <div className="mx-auto max-w-3xl">
                {heroLogoURL && (
                  <div className="flex justify-center py-4">
                    <img src={heroLogoURL} alt="Capsy" className="h-12 w-auto opacity-90" onError={(e)=>{ e.currentTarget.onerror=null; e.currentTarget.src='/assets/capsy-logo.png'; }}/>
                  </div>
                )}
                <div className="inline-flex items-center gap-2 rounded-full border border-indigo-400/30 bg-indigo-500/10 px-3 py-1 text-xs text-indigo-300">New • AI Captioning</div>
                <h1 className="mt-4 text-4xl md:text-6xl font-extrabold tracking-tight">Add engaging captions to any video in minutes</h1>
                <p className="mt-4 text-neutral-300">Capsy auto-detects speech, lets you style per-word highlights, and exports social-ready videos.
                  Start free with monthly tokens; upgrade for more and remove limits.</p>
                <div className="mt-8 flex justify-center gap-3">
                  <a href="#/editor" className="px-6 py-3 rounded-xl bg-indigo-600 hover:bg-indigo-500 font-semibold shadow-lg shadow-indigo-600/25">Try Capsy</a>
                  <a href="#/pricing" className="px-6 py-3 rounded-xl bg-neutral-900/60 hover:bg-neutral-800 border border-neutral-700">View pricing</a>
                </div>
              </div>
              <div className="pointer-events-none absolute -right-24 -bottom-24 h-72 w-72 rounded-full bg-indigo-600/20 blur-3xl" />
            </section>
            <section className="mt-10 grid md:grid-cols-3 gap-4">
              <div className="p-5 rounded-2xl bg-neutral-900/70 border border-neutral-800">
                <div className="text-lg font-semibold">Auto‑transcribe</div>
                <p className="text-sm text-neutral-400 mt-1">Drop in your video and get clean, editable captions in minutes.</p>
              </div>
              <div className="p-5 rounded-2xl bg-neutral-900/70 border border-neutral-800">
                <div className="text-lg font-semibold">Per‑word styling</div>
                <p className="text-sm text-neutral-400 mt-1">Emphasize important words with color, size and subtle motion.</p>
              </div>
              <div className="p-5 rounded-2xl bg-neutral-900/70 border border-neutral-800">
                <div className="text-lg font-semibold">Simple pricing</div>
                <p className="text-sm text-neutral-400 mt-1">Free to start with monthly tokens. Upgrade for more and remove limits.</p>
              </div>
            </section>
            <section className="mt-10 grid md:grid-cols-3 gap-4">
              <div className="p-5 rounded-2xl bg-neutral-900/70 border border-neutral-800">
                <div className="text-lg font-semibold">Auto‑transcribe</div>
                <p className="text-sm text-neutral-400 mt-1">Upload a video and Capsy creates clean, editable captions.</p>
              </div>
              <div className="p-5 rounded-2xl bg-neutral-900/70 border border-neutral-800">
                <div className="text-lg font-semibold">Per‑word styling</div>
                <p className="text-sm text-neutral-400 mt-1">Emphasize key words with color, size, and subtle motion.</p>
              </div>
              <div className="p-5 rounded-2xl bg-neutral-900/70 border border-neutral-800">
                <div className="text-lg font-semibold">One‑click exports</div>
                <p className="text-sm text-neutral-400 mt-1">Bake captions into your video and share anywhere.</p>
              </div>
            </section>
          </main>
        </div>
      );
    }
    function LoginPage(){
      const [email, setEmail] = useState("");
      return (
        <div className="min-h-screen">
          <header className="sticky top-0 z-10 border-b theme-border site-header backdrop-blur-xl">
            <div className="mx-auto max-w-7xl px-4 py-3 flex items-center justify-between">
              <div className="flex items-center gap-3">
                 {miniLogoURL ? (
                  <a href="#/" className="inline-flex items-center">
                    <img src={miniLogoURL} alt="Capsy" className="h-8 w-auto" onError={(e)=>{ e.currentTarget.onerror=null; e.currentTarget.src='/assets/capsy-logo-mini.png'; }}/>
                  </a>
                ) : (
                  <div className="font-semibold tracking-wide theme-strong">Capsy</div>
                )}
                <a href="#/editor" className="ml-4 theme-muted hover:theme-strong text-sm">Editor</a>
                <a href="#/pricing" className="ml-2 theme-muted hover:theme-strong text-sm">Pricing</a>
              </div>
              <div></div>
            </div>
          </header>
          <main className="mx-auto max-w-md p-6">
            <div className="p-6 rounded-2xl bg-neutral-900/70 border border-neutral-800">
              <h2 className="text-xl font-semibold mb-2">Log in to Capsy</h2>
              <p className="text-neutral-400 text-sm mb-4">Sign in with your Google account.</p>
              <button onClick={()=>{ window.location.href = '/api/oauth_google_start'; }} className="w-full px-4 py-2 rounded-lg bg-white/95 hover:bg-white text-neutral-900 font-semibold flex items-center justify-center gap-2">
                <span className="inline-block h-4 w-4 bg-[url('data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 48 48%22%3E%3Cpath fill=%22%234285F4%22 d=%22M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.9-6.9C35.9 2.3 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l8.01 6.22C12.48 13.02 17.74 9.5 24 9.5z%22/%3E%3Cpath fill=%23%23EA4335 d=%22M46.5 24c0-1.54-.14-3.02-.39-4.44H24v8.41h12.65c-.55 2.96-2.21 5.48-4.7 7.17l7.17 5.56C43.83 36.89 46.5 30.89 46.5 24z%22/%3E%3Cpath fill=%22%23FBBC05%22 d=%22M10.57 28.44A14.5 14.5 0 019.5 24c0-1.53.26-3 .73-4.38l-8.01-6.22A23.94 23.94 0 000 24c0 3.89.93 7.56 2.56 10.78l8.01-6.34z%22/%3E%3Cpath fill=%22%2334A853%22 d=%22M24 48c6.48 0 11.93-2.13 15.91-5.81l-7.17-5.56c-2.02 1.35-4.61 2.15-8.74 2.15-6.26 0-11.52-3.52-13.43-8.33l-8.01 6.34C6.51 42.62 14.62 48 24 48z%22/%3E%3C/svg%3E')] bg-no-repeat bg-center"></span>
                Continue with Google
              </button>
            </div>
          </main>
        </div>
      );
    }
    function PricingPage(){
      const user = Auth.getUser();
      return (
        <div className="min-h-screen">
          <header className="sticky top-0 z-10 border-b theme-border site-header backdrop-blur-xl">
            <div className="mx-auto max-w-7xl px-4 py-3 flex items-center justify-between">
              <div className="flex items-center gap-3">
                 {miniLogoURL ? (
                  <a href="#/" className="inline-flex items-center">
                    <img src={miniLogoURL} alt="Capsy" className="h-8 w-auto" onError={(e)=>{ e.currentTarget.onerror=null; e.currentTarget.src='/assets/capsy-logo-mini.png'; }}/>
                  </a>
                ) : (
                  <a href="#/" className="font-semibold tracking-wide theme-strong">Capsy</a>
                )}
                <a href="#/editor" className="ml-4 theme-muted hover:theme-strong text-sm">Editor</a>
                <a href="#/pricing" className="ml-2 theme-muted hover:theme-strong text-sm">Pricing</a>
              </div>
              <div className="flex items-center gap-2">
                {user ? (
                  <>
                    <div className="hidden sm:flex items-center gap-2 px-3 py-1.5 rounded-xl btn-outline text-sm">
                      <span className="font-mono truncate max-w-[16ch]" title={user.email}>{user.email}</span>
                      <span className="text-neutral-400">·</span>
                      <span>{Auth.getEntitlement(user.id).tokensRemaining} tokens</span>
                      <span className="text-neutral-400">·</span>
                      <span>{Auth.getEntitlement(user.id).subscribed ? 'Pro' : 'Free'}</span>
                    </div>
                    <a href="#/account" className="px-3 py-1.5 rounded-xl btn-outline text-sm">Account</a>
                  </>
                ) : (
                  <a href="#/login" className="px-3 py-1.5 rounded-xl btn-accent text-sm">Log in</a>
                )}
              </div>
            </div>
          </header>
          <main className="mx-auto max-w-4xl p-6">
            <h2 className="text-3xl font-extrabold">Simple pricing</h2>
            <div className="mt-6 grid md:grid-cols-2 gap-4">
              <div className="p-6 rounded-2xl bg-neutral-900/70 border border-neutral-800">
                <div className="text-sm uppercase tracking-wider text-neutral-400">Free</div>
                <div className="text-4xl font-extrabold mt-1">$0</div>
                <ul className="mt-4 text-neutral-300 text-sm space-y-1">
                  <li>100 tokens / month</li>
                  <li>Auto‑detect language</li>
                  <li>Watermarked exports</li>
                  <li>Basic processing priority</li>
                </ul>
                <a href="#/editor" className="mt-4 inline-block px-4 py-2 rounded-lg bg-neutral-800 hover:bg-neutral-700 border border-neutral-700">Start free</a>
              </div>
              <div className="p-6 rounded-2xl bg-neutral-900/70 border border-neutral-800 ring-1 ring-white/5">
                <div className="text-sm uppercase tracking-wider text-amber-400">Pro</div>
                <div className="text-4xl font-extrabold mt-1">$9<span className="text-lg text-neutral-400">/mo</span></div>
                <ul className="mt-4 text-neutral-300 text-sm space-y-1">
                  <li>1000 tokens / month</li>
                  <li>No watermark on exports</li>
                  <li>Priority processing</li>
                  <li>All 102 supported languages</li>
                </ul>
                <button onClick={()=>{ const u = Auth.getUser(); if(!u){ location.hash = '#/login'; return; } Auth.setSubscribed(u.id, true); location.hash = '#/account'; }} className="mt-4 inline-block px-4 py-2 rounded-lg bg-indigo-600 hover:bg-indigo-500 font-semibold">Subscribe</button>
              </div>
            </div>
            <div className="mt-8 p-4 rounded-2xl bg-neutral-900/70 border border-neutral-800">
              <div className="text-lg font-semibold">How tokens work</div>
              <p className="text-sm text-neutral-400 mt-1">Tokens are monthly captioning credits. Free includes 100 tokens/month; Pro includes 1000 tokens/month. Auto‑detect language and exports consume a small number of tokens. Your balance resets monthly.</p>
            </div>
            <div className="mt-6 p-4 rounded-2xl bg-neutral-900/70 border border-neutral-800">
              <details>
                <summary className="text-lg font-semibold cursor-pointer select-none">
                  Supported languages (102)
                  <span className="ml-2 text-sm text-neutral-400">(click to expand)</span>
                </summary>
                <div className="text-sm text-neutral-300 mt-2 grid md:grid-cols-3 gap-2">
                  {['Arabic','Bulgarian','Chinese','Croatian','Czech','Danish','Dutch','English','Estonian','Finnish','French','German','Greek','Hebrew','Hindi','Hungarian','Indonesian','Italian','Japanese','Korean','Latvian','Lithuanian','Malay','Norwegian Bokmål','Polish','Portuguese','Romanian','Russian','Serbian','Slovak','Slovenian','Spanish','Swedish','Tagalog','Thai','Turkish','Ukrainian','Vietnamese','Urdu','Farsi','Catalan','Icelandic','Irish','Welsh','Basque','Galician','Afrikaans','Albanian','Armenian','Azerbaijani','Belarusian','Bengali','Bosnian','Georgian','Gujarati','Kazakh','Khmer','Kyrgyz','Lao','Macedonian','Maltese','Mongolian','Nepali','Pashto','Punjabi','Sinhala','Somali','Swahili','Tamil','Telugu','Tibetan','Uzbek','Yoruba','Zulu','Marathi','Kannada','Malayalam','Luxembourgish','Haitian Creole','Maori','Quechua','Kurdish','Amharic','Tigrinya','Hausa','Igbo','Burmese','Tagalog (Filipino)','Flemish','Scots Gaelic','Sundanese','Javanese','Xhosa','Frisian','Corsican','Samoan','Māori','Esperanto','Latin','Galician (GL)','Occitan','Breton','Yiddish','Ladino','Interlingua'].map((lang)=> (
                    <span key={lang} className="block">{lang}</span>
                  ))}
                </div>
              </details>
            </div>
          </main>
        </div>
      );
    }
    function AccountPage(){
      const [, force] = useState(0);
      const user = Auth.getUser();
      if (!user) return <LoginPage/>;
      const ent = Auth.getEntitlement(user.id);
      return (
        <div className="min-h-screen">
          <header className="sticky top-0 z-10 border-b theme-border site-header backdrop-blur-xl">
            <div className="mx-auto max-w-7xl px-4 py-3 flex items-center justify-between">
              <div className="flex items-center gap-3">
                {miniLogoURL ? (
                  <a href="#/" className="inline-flex items-center">
                    <img src={miniLogoURL} alt="Capsy" className="h-8 w-auto" onError={(e)=>{ e.currentTarget.onerror=null; e.currentTarget.src='/assets/capsy-logo-mini.png'; }}/>
                  </a>
                ) : (
                  <a href="#/" className="font-semibold tracking-wide theme-strong">Capsy</a>
                )}
                <a href="#/editor" className="ml-4 theme-muted hover:theme-strong text-sm">Editor</a>
                <a href="#/pricing" className="ml-2 theme-muted hover:theme-strong text-sm">Pricing</a>
              </div>
              <div className="flex items-center gap-2">
                {user && (
                  <div className="hidden sm:flex items-center gap-2 px-3 py-1.5 rounded-xl btn-outline text-sm">
                    <span className="font-mono truncate max-w-[16ch]" title={user.email}>{user.email}</span>
                    <span className="text-neutral-400">·</span>
                    <span>{ent.tokensRemaining} tokens</span>
                    <span className="text-neutral-400">·</span>
                    <span>{ent.subscribed ? 'Pro' : 'Free'}</span>
                  </div>
                )}
              </div>
            </div>
          </header>
          <main className="mx-auto max-w-lg p-6">
            <div className="p-6 rounded-2xl bg-neutral-900/70 border border-neutral-800 space-y-3">
              <div className="text-xl font-semibold">Account</div>
              <div className="text-sm text-neutral-300">Signed in as <span className="font-mono">{user.email}</span></div>
              <div className="text-sm text-neutral-300">Subscription: {ent.subscribed ? <span className="text-emerald-400">Pro</span> : <span className="text-neutral-400">Free</span>}</div>
              <div className="text-sm text-neutral-300">Plan details: {ent.subscribed ? '1000 tokens / month, no watermark, priority processing.' : '100 tokens / month, auto‑detect language, watermarked exports.'}</div>
              <div className="text-sm text-neutral-300">Tokens: <span className="font-semibold">{ent.tokensRemaining}</span> / {ent.allowance} <span className="text-neutral-400">(reset {new Date(ent.resetISO).toLocaleDateString()})</span></div>
              <div className="pt-2 flex gap-2">
                {ent.subscribed ? (
                  <button onClick={()=>{ Auth.setSubscribed(user.id, false); force(x=>x+1); }} className="px-3 py-1.5 rounded-lg bg-neutral-800 hover:bg-neutral-700 border border-neutral-700 text-sm">Cancel subscription</button>
                ) : (
                  <a href="#/pricing" className="px-3 py-1.5 rounded-lg bg-indigo-600 hover:bg-indigo-500 text-sm font-semibold">Upgrade</a>
                )}
                <button onClick={()=>{ Auth.logout(); location.hash = '#/'; }} className="px-3 py-1.5 rounded-lg bg-rose-600 hover:bg-rose-500 text-sm">Log out</button>
              </div>
              <div className="pt-2 text-xs text-neutral-500">
                Supported languages: 102. Pick a target language in the editor, or use Auto‑detect which consumes a small number of tokens.
              </div>
            </div>
          </main>
        </div>
      );
    }
    function MainApp(){
      const route = useHashRoute();
      const path = route.split('?')[0];
       if (path === 'editor') { const user = Auth.getUser(); return user ? <EditorApp/> : <LoginPage/>; }
      if (path === 'pricing') return <PricingPage/>;
      if (path === 'account') return <AccountPage/>;
      if (path === 'login') return <LoginPage/>;
      return <LandingPage/>;
    }

    function EditorApp(){
      const [videoURL, setVideoURL] = useState(null);
      const [videoFile, setVideoFile] = useState(null);
      const [duration, setDuration] = useState(0);
      const [segments, setSegments] = useState([]);
      const [activeIdx, setActiveIdx] = useState(-1);
      const [isProcessing, setIsProcessing] = useState(false);
      const [processingMode, setProcessingMode] = useState(null); // 'transcribe' | 'export' | null
      const [processingError, setProcessingError] = useState(null);
      const [overlayText, setOverlayText] = useState("");
      const [activeWord, setActiveWord] = useState(null);
      const [captionStyle, setCaptionStyle] = useState({
        fontFamily: 'Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif',
        fontWeight: 800,
        fontSize: 36,
        yOffsetPercent: 40,
        color: '#ffffff',
        shadowColor: '#000000',
        shadowSize: 6,
        uppercase: true,
        emphasizeColor: '#f59e0b',
        emphasizeScale: 1.35,
        wordEffect: 'pop',
        effectOnlyEmphasized: false,
        effectDurationMs: 250,
      });
      const [wordEdit, setWordEdit] = useState(null); // { segIndex, wordIndex, value }
      const [tick, setTick] = useState(0);
      const [autoCaptioned, setAutoCaptioned] = useState(false);
      const [styleTab, setStyleTab] = useState('style');
      const [selectedSegs, setSelectedSegs] = useState([]);
      const [videoFX, setVideoFX] = useState({
        saturate: 1,
        contrast: 1,
        brightness: 1,
        hue: 0,
        sepia: 0,
        grayscale: 0,
        invert: 0,
        blur: 0,
      });
      const [wordsPerUnit, setWordsPerUnit] = useState(1);
      // Draft time editing per segment index so users can freely type and backspace
      const [startDrafts, setStartDrafts] = useState({}); // { [index]: string }
      const [endDrafts, setEndDrafts] = useState({});   // { [index]: string }
      const [languageChoice, setLanguageChoice] = useState('auto');
      const [showLangConfirm, setShowLangConfirm] = useState(false);
      const [autoStartOnUpload] = useState(false);
      const videoRef = useRef(null);
      const playerRef = useRef(null);
      const inputRef = useRef(null);
      const user = Auth.getUser();
      const [entitlement, setEntitlement] = useState(()=> user ? Auth.getEntitlement(user.id) : null);
      // logoURL is now global; keep a local alias if needed
      const logoKey = window.__CAPSY_LOGO_KEY__ || null;

      const totalWidth = 1000; // px for timeline scaling

      const videoFilterCSS = useMemo(() => {
        const parts = [];
        parts.push(
          `saturate(${Number(videoFX.saturate||1)})`,
          `contrast(${Number(videoFX.contrast||1)})`,
          `brightness(${Number(videoFX.brightness||1)})`,
          `hue-rotate(${Number(videoFX.hue||0)}deg)`,
          `sepia(${Number(videoFX.sepia||0)})`,
          `grayscale(${Number(videoFX.grayscale||0)})`,
          `invert(${Number(videoFX.invert||0)})`,
          `blur(${Number(videoFX.blur||0)}px)`
        );
        return parts.join(' ');
      }, [videoFX]);

      const words = useMemo(()=>{
        // Derive display units by grouping a fixed number of words per unit
        const result = [];
        const chunk = Math.max(1, Math.floor(Number(wordsPerUnit)||1));
        const d = (videoRef.current?.duration || duration) || Infinity;
        segments.forEach((seg, segIndex) => {
          const text = (seg.text || '').trim();
          if (!text) return;
          const tokens = text.split(/\s+/);
          const segDur = Math.max(0.001, (seg.end ?? seg.start) - seg.start);
          let durs = (Array.isArray(seg.wordDurations) && seg.wordDurations.length === tokens.length)
            ? seg.wordDurations.slice()
            : tokens.map(()=>segDur/Math.max(1,tokens.length));
          // Normalize to segment duration
          const sum = durs.reduce((a,b)=>a+b,0);
          const scale = sum>0 ? (segDur / sum) : 1;
          durs = durs.map(dur=>dur*scale);
          let t = seg.start;
          for (let i = 0; i < tokens.length; i += chunk) {
            const endIdx = Math.min(tokens.length, i + chunk) - 1;
            const groupTokens = tokens.slice(i, endIdx + 1);
            const groupDur = durs.slice(i, endIdx + 1).reduce((a,b)=>a+b,0);
            const emphasized = Boolean(seg.emphasizeSegment) || groupTokens.some((_tok, j)=> Boolean(seg.emphasizedWordIndices?.includes(i+j)));
            const start = t;
            let end = Math.min(seg.end, start + groupDur);
            // Ensure the last group in a segment remains visible at the boundary
            if (endIdx === tokens.length - 1) {
              const cap = Number.isFinite(d) ? d : Infinity;
              end = Math.min(cap, end + 0.04); // small epsilon to cover end boundary
            }
            result.push({ text: groupTokens.join(' '), start, end, segIndex, wordIndex: endIdx, emphasized });
            t = end;
          }
        });
        return result.sort((a,b)=>a.start-b.start);
      }, [segments, wordsPerUnit, duration, videoRef.current?.duration]);

      function resetToUploadState(){
        try { if (videoURL) URL.revokeObjectURL(videoURL); } catch(_){}
        setVideoURL(null);
        setVideoFile(null);
        setSegments([]);
        setActiveIdx(-1);
        setAutoCaptioned(false);
        setLanguageChoice('auto');
        setShowLangConfirm(false);
      }

      useEffect(() => {
        const v = videoRef.current;
        if (!v) return;
        let rafId;
        function loop(){
          const t = v.currentTime;
          const seg = segments.find((s)=>t >= s.start && t < s.end);
          const txt = seg ? seg.text : "";
          if (txt !== overlayText) setOverlayText(txt);
          const idx = seg ? segments.indexOf(seg) : -1;
          // active word (one at a time)
          const w = words.find((w)=>t >= w.start && t < (w.end + 0.02)) || null;
          const sameWord = (w && activeWord) ? (w.start===activeWord.start && w.end===activeWord.end) : (w===activeWord);
          if (!sameWord) setActiveWord(w);
          if (idx !== activeIdx) setActiveIdx(idx);
          setTick(prev=>prev+1);
          rafId = requestAnimationFrame(loop);
        }
        rafId = requestAnimationFrame(loop);
        return () => cancelAnimationFrame(rafId);
      }, [segments, words, overlayText, activeWord, activeIdx]);

      async function onFilesSelected(files) {
        const file = files?.[0];
        if (!file) return;
        if (!file.type.startsWith("video/")) {
          alert("Please select a video file.");
          return;
        }
        // Pre-check size against server limits to avoid network-level 413 and staying in editor
        try {
          const lim = await fetch('/api/limits', { credentials: 'include' }).then(r => r.ok ? r.json() : Promise.reject());
          const maxMb = Number(lim?.maxUploadMb || 0);
          if (maxMb > 0 && file.size > maxMb * 1024 * 1024) {
            resetToUploadState();
            alert(`File is too large. Max ${maxMb} MB.`);
            return;
          }
        } catch(_) {}
        if (videoURL) URL.revokeObjectURL(videoURL);
        const url = URL.createObjectURL(file);
        setVideoURL(url);
        setVideoFile(file);
        // Ensure audio is audible for preview
        setTimeout(()=>{ try { if (videoRef.current) videoRef.current.muted = false; } catch(_){} }, 0);
        setSegments([]);
        setActiveIdx(-1);
        setAutoCaptioned(false);
        setLanguageChoice('auto');
        setShowLangConfirm(true);
      }

      function onDrop(e) {
        e.preventDefault();
        onFilesSelected(e.dataTransfer.files);
      }

      async function handleGenerate() {
        if (!videoRef.current) return;
        const dur = videoRef.current.duration || duration;
        if (!Number.isFinite(dur) || dur <= 0) {
          alert("Video duration not ready yet—try play/pause once and retry.");
          return;
        }
        const user = window.__APP_AUTH__?.getUser?.();
        if (!user) { alert('Please log in to transcribe.'); location.hash = '#/login'; return; }
        // Deduct tokens only when using Auto-detect language
        const transcribeCost = (languageChoice === 'auto') ? 5 : 0;
        if (transcribeCost > 0) {
          try {
            const ent = window.__APP_AUTH__?.getEntitlement?.(user.id);
            const remaining = ent?.tokensRemaining ?? 0;
            if (remaining < transcribeCost) {
              alert(`Not enough tokens to transcribe. Required: ${transcribeCost}. Visit Pricing to upgrade.`);
              location.hash = '#/pricing';
              return;
            }
          } catch(_) {}
        }
        setProcessingMode('transcribe'); setProcessingError(null); setIsProcessing(true);
        try {
          // Always upload and process on the server. No browser-side audio processing.
          const segs = await transcribeFileViaAPI(videoFile, { language: languageChoice !== 'auto' ? languageChoice : undefined });
          // Post-process: split into words and set even per-word durations so overlay animates per word.
          const stripPunct = (txt)=> (txt||'').replace(/[\.,!?;:\-–—"'"“"‘'()\[\]…]/g, '').replace(/\s+/g,' ').trim();
          const normalized = segs.map(s => {
            const text = String(s.text||'').trim();
            const start = Math.max(0, Number(s.start)||0);
            const end = Math.max(start + 0.01, Number(s.end)||start);
            const clean = stripPunct(text);
            const tokens = clean ? clean.split(/\s+/) : [];
            const segDur = Math.max(0.001, end - start);
            const wordDurations = tokens.length ? tokens.map(()=> segDur/Math.max(1,tokens.length)) : [];
            return { start, end, text: clean, wordDurations, userAdded: false };
          });
          setSegments(normalized);
          // Only deduct tokens after a successful transcription result
          if (transcribeCost > 0) {
            try {
              window.__APP_AUTH__?.consumeTokens?.(user.id, transcribeCost);
              setEntitlement(window.__APP_AUTH__?.getEntitlement?.(user.id));
            } catch(_) {}
          }
        } catch (e) {
          console.error(e);
          const msg = (e && e.message) ? String(e.message) : 'Transcription failed. Please try again.';
          setProcessingError(msg);
          // Return to upload screen on failure
          resetToUploadState();
        } finally {
          setIsProcessing(false); setProcessingMode(null);
        }
      }

      async function importCaptions(e) {
        const file = e.target.files?.[0];
        if (!file) return;
        const text = await file.text();
        const segs = parseSRTorVTT(text).map(s => ({ ...s, userAdded: false }));
        if (!segs.length) { alert("Couldn't parse captions."); return; }
        setSegments(segs);
        setActiveIdx(-1);
        e.target.value = "";
      }

      function exportSRT() { downloadText("captions.srt", segmentsToSRT(segments)); }
      function exportVTT() { downloadText("captions.vtt", segmentsToVTT(segments)); }

      function updateSegment(i, patch){
        setSegments(prev => prev.map((s,idx) => {
          if (idx !== i) return s;
          const next = { ...s, ...patch };
          const textPrev = (s.text||'').trim();
          const textNext = (next.text||'').trim();
          const prevTokens = textPrev ? textPrev.split(/\s+/) : [];
          const nextTokens = textNext ? textNext.split(/\s+/) : [];
          // If start/end changed, scale wordDurations to preserve relative proportions
          const startChanged = Object.prototype.hasOwnProperty.call(patch, 'start');
          const endChanged = Object.prototype.hasOwnProperty.call(patch, 'end');
          if ((startChanged || endChanged) && Array.isArray(s.wordDurations) && s.wordDurations.length === prevTokens.length && prevTokens.length>0) {
            const oldDur = Math.max(0.001, (s.end - s.start));
            const newDur = Math.max(0.001, ((endChanged?patch.end:s.end) - (startChanged?patch.start:s.start)));
            if (Number.isFinite(oldDur) && Number.isFinite(newDur) && oldDur > 0) {
              const scale = newDur / oldDur;
              next.wordDurations = s.wordDurations.map(d => d * scale);
            }
          }
          // If text changed and token count differs, re-init wordDurations evenly
          if (Object.prototype.hasOwnProperty.call(patch, 'text')) {
            if (!Array.isArray(s.wordDurations) || s.wordDurations.length !== nextTokens.length) {
              const segDur = Math.max(0.001, ((endChanged?patch.end:s.end) - (startChanged?patch.start:s.start)));
              next.wordDurations = nextTokens.length ? nextTokens.map(()=>segDur/Math.max(1,nextTokens.length)) : [];
            } else {
              next.wordDurations = s.wordDurations.slice();
            }
          }
          return next;
        }));
      }

      function adjustWordDuration(segIndex, wordIndex, deltaSeconds){
        setSegments(prev => prev.map((seg, idx) => {
          if (idx !== segIndex) return seg;
          const text = (seg.text||'').trim();
          const tokens = text ? text.split(/\s+/) : [];
          if (!tokens.length) return seg;
          const segDur = Math.max(0.001, (seg.end - seg.start));
          let durs = (Array.isArray(seg.wordDurations) && seg.wordDurations.length === tokens.length)
            ? seg.wordDurations.slice()
            : tokens.map(()=>segDur/Math.max(1,tokens.length));
          const minDur = 0.05;
          let neighbor = (wordIndex + 1 < durs.length) ? wordIndex + 1 : (wordIndex - 1);
          if (neighbor < 0 || neighbor >= durs.length) return seg;
          if (deltaSeconds > 0) {
            const give = Math.min(deltaSeconds, durs[neighbor] - minDur);
            if (give <= 0) return seg;
            durs[wordIndex] += give;
            durs[neighbor] -= give;
          } else if (deltaSeconds < 0) {
            const take = Math.min(-deltaSeconds, durs[wordIndex] - minDur);
            if (take <= 0) return seg;
            durs[wordIndex] -= take;
            durs[neighbor] += take;
          }
          // Normalize to segment duration
          const sum = durs.reduce((a,b)=>a+b,0);
          const scale = sum>0 ? (segDur / sum) : 1;
          durs = durs.map(d=>d*scale);
          return { ...seg, wordDurations: durs };
        }));
      }

      function addSegmentAtCurrent() {
        if (!videoRef.current) return;
        const t = videoRef.current.currentTime;
        const seg = { start: t, end: t + 2.0, text: "", userAdded: true };
        setSegments(prev => [...prev, seg].sort((a,b)=>a.start-b.start));
      }
      function deleteSegment(i) {
        setSegments(prev => prev.filter((_,idx)=>idx!==i));
        if (activeIdx === i) setActiveIdx(-1);
      }

      const timelineLayers = useMemo(() => {
        const d = (videoRef.current?.duration || duration) || 1;
        const base = [];
        const added = [];
        segments.forEach((s, i) => {
          const left = (s.start/d)*totalWidth;
          const width = Math.max(8, ((s.end - s.start)/d)*totalWidth);
          const item = { i, left, width };
          if (s.userAdded) added.push(item); else base.push(item);
        });
        return { base, added };
      }, [segments, duration, videoRef.current?.duration]);
      const timelineWords = useMemo(() => {
        const d = (videoRef.current?.duration || duration) || 1;
        return words.map((w, i) => {
          const left = (w.start/d)*totalWidth;
          const width = Math.max(2, ((w.end - w.start)/d)*totalWidth);
          return { i, left, width };
        });
      }, [words, duration, videoRef.current?.duration]);

      function onLoadedMetadata() {
        const d = videoRef.current?.duration || 0;
        setDuration(d);
      }

      // Auto-generate captions after upload once duration is known
      useEffect(() => {
        const dur = videoRef.current?.duration || duration;
        if (autoStartOnUpload && videoFile && Number.isFinite(dur) && dur > 0 && !autoCaptioned && !isProcessing) {
          handleGenerate();
          setAutoCaptioned(true);
        }
      }, [videoFile, duration, isProcessing, autoStartOnUpload]);

      // Expose state to exporter
      useEffect(()=>{
        window.__CAPTIONER_APP_STATE__ = {
          getSegments: () => segments,
          getCaptionStyle: () => captionStyle,
          getVideoFile: () => videoFile,
          getVideoFX: () => videoFX,
        };
      }, [segments, captionStyle, videoFile, videoFX]);

      function jumpTo(i) {
        const s = segments[i];
        if (!s || !videoRef.current) return;
        videoRef.current.currentTime = s.start + 0.01;
        setActiveIdx(i);
      }
      function jumpToTime(t) {
        if (!videoRef.current || !Number.isFinite(t)) return;
        videoRef.current.currentTime = Math.max(0, t + 0.01);
      }

      const containerClasses = "rounded-2xl bg-neutral-900/70 backdrop-blur border border-neutral-800";

      return (
        <div className="min-h-screen">
          {/* Header */}
          <header className="sticky top-0 z-10 border-b theme-border site-header backdrop-blur-xl">
            <div className="mx-auto max-w-7xl px-4 py-3 flex items-center justify-between">
              <div className="flex items-center gap-3">
                 {miniLogoURL ? (
                  <a href="#/" className="inline-flex items-center">
                    <img src={miniLogoURL} alt="Capsy" className="h-8 w-auto" onError={(e)=>{ e.currentTarget.onerror=null; e.currentTarget.src='/assets/capsy-logo-mini.png'; }}/>
                  </a>
                ) : (
                  <a href="#/" className="font-semibold tracking-wide theme-strong">Capsy</a>
                )}
                <a href="#/pricing" className="theme-muted text-sm hidden sm:inline hover:theme-strong">Pricing</a>
              </div>
              <div className="flex items-center gap-2">
                {user && entitlement && (
                  <div className="hidden sm:flex items-center gap-2 px-3 py-1.5 rounded-xl btn-outline text-sm">
                    <span className="font-mono truncate max-w-[16ch]" title={user.email}>{user.email}</span>
                    <span className="text-neutral-400">·</span>
                    <span title="Tokens remaining">{entitlement.tokensRemaining} tokens</span>
                  </div>
                )}
                {videoURL ? (
                  <>
                    {/* Language selector */}
                    <select
                      value={languageChoice}
                      onChange={(e)=>setLanguageChoice(e.target.value)}
                      title="Language"
                      className="px-2 py-1.5 rounded-xl bg-neutral-800/60 border border-neutral-700 text-sm"
                    >
                      <option value="auto">Auto‑detect</option>
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                      <option value="it">Italian</option>
                      <option value="pt">Portuguese</option>
                      <option value="nl">Dutch</option>
                      <option value="pl">Polish</option>
                      <option value="ru">Russian</option>
                      <option value="tr">Turkish</option>
                      <option value="uk">Ukrainian</option>
                      <option value="ar">Arabic</option>
                      <option value="hi">Hindi</option>
                      <option value="ja">Japanese</option>
                      <option value="ko">Korean</option>
                      <option value="zh">Chinese</option>
                      <option value="vi">Vietnamese</option>
                      <option value="sv">Swedish</option>
                      <option value="no">Norwegian Bokmål</option>
                      <option value="da">Danish</option>
                      <option value="fi">Finnish</option>
                    </select>
                    <button onClick={handleGenerate}
                            disabled={!videoURL || isProcessing}
                            title={!videoURL? 'Upload a video first' : 'Generate captions'}
                            className={`px-3 py-1.5 rounded-xl text-sm ring-1 ring-white/10 ${isProcessing? 'bg-neutral-800/40 text-neutral-500 cursor-not-allowed' : 'bg-neutral-800/60 hover:bg-neutral-700'}`}>
                      {isProcessing ? 'Transcribing…' : 'Generate'}
                    </button>
                    <button onClick={async ()=>{
                              const user = window.__APP_AUTH__?.getUser?.();
                              if (!user) { alert('Please log in to export.'); location.hash = '#/login'; return; }
                              const ent = window.__APP_AUTH__?.getEntitlement?.(user.id);
                              const cost = 5; // export cost
                              // Do NOT consume tokens here; only after success
                              if ((ent?.tokensRemaining ?? 0) < cost) {
                                alert(`Not enough tokens for export. Required: ${cost}. Visit Pricing to upgrade.`);
                                location.hash = '#/pricing';
                                return;
                              }
                              const willWatermark = !!ent?.watermark;
                              try {
                                setProcessingMode('export'); setProcessingError(null); setIsProcessing(true);
                                const app = window.__CAPTIONER_APP_STATE__ || {};
                                const segments = app.getSegments ? app.getSegments() : [];
                                const captionStyle = app.getCaptionStyle ? app.getCaptionStyle() : {};
                                const videoFX = app.getVideoFX ? app.getVideoFX() : {};
                                // Pass style through mostly unchanged - let exporter handle sizing
                                const scaledStyle = { ...captionStyle };
                                const srcKey = (window.__LAST_UPLOAD_KEY__ || null);
                                if (!srcKey) { alert('No uploaded source found. Please re-upload the video.'); return; }
                                const spec = {
                                  source: { r2Key: srcKey },
                                  segments,
                                  captionStyle: scaledStyle,
                                  videoFX,
                                  watermark: willWatermark,
                                  output: { codec: 'h264', audioCodec: 'aac' }
                                };
                                const r = await fetch('/api/export', { method: 'POST', credentials: 'include', headers: { 'content-type': 'application/json' }, body: JSON.stringify(spec) });
                                if (!r.ok) { const t = await r.text().catch(()=> ''); throw new Error(t || 'Export create failed'); }
                                const { jobId } = await r.json();
                                setIsProcessing(true);
                                const start = Date.now();
                                while (true) {
                                  await new Promise(res=>setTimeout(res, 3000));
                                  const s = await fetch(`/api/export_status?jobId=${encodeURIComponent(jobId)}`, { credentials: 'include' });
                                  if (!s.ok) { const t = await s.text().catch(()=> ''); throw new Error(t || 'Status failed'); }
                                  const data = await s.json();
                                  if (data.status === 'completed' && data.downloadURL) {
                                    const a = document.createElement('a');
                                    a.href = data.downloadURL;
                                    a.download = 'export.mp4';
                                    a.click();
                                    try {
                                      // Consume export tokens only on success
                                      window.__APP_AUTH__?.consumeTokens?.(user.id, cost);
                                      setEntitlement(window.__APP_AUTH__?.getEntitlement?.(user.id));
                                    } catch(_) {}
                                    break;
                                  }
                                  if (data.status === 'error') {
                                    throw new Error(data.error || 'Export failed');
                                  }
                                  if (Date.now() - start > 60 * 60 * 1000) {
                                    throw new Error('Export timed out');
                                  }
                                }
                              } catch (e) {
                                console.error(e);
                                const msg = (e && e.message) ? String(e.message) : 'Server export failed.';
                                setProcessingError(msg);
                              } finally {
                                setIsProcessing(false);
                              }
                            }}
                            title="Export video with baked captions"
                            className="px-3 py-1.5 rounded-xl bg-neutral-800/60 hover:bg-neutral-700 text-sm ring-1 ring-white/10">
                      Export
                    </button>
                  </>
                ) : null}
                <button onClick={()=>inputRef.current?.click()}
                        className="px-3 py-1.5 rounded-xl bg-emerald-600 hover:bg-emerald-500 text-sm shadow-lg shadow-emerald-500/20">
                  Upload
                </button>
                <input ref={inputRef} className="hidden" type="file" accept="video/*"
                       onChange={e=>onFilesSelected(e.target.files)} />
              </div>
            </div>
          </header>

          <main className="mx-auto max-w-7xl p-4">
            {/* Upload zone */}
            {!videoURL && (
              <div
                onDragOver={(e)=>e.preventDefault()}
                onDrop={onDrop}
                className={`border-2 border-dashed border-neutral-700 hover:border-neutral-500 transition ${containerClasses} p-10 grid place-items-center text-center`}
              >
                <div>
                  <div className="text-2xl font-semibold mb-2">Upload a video</div>
                  <p className="text-neutral-400 mb-4">Drag & drop or click to select a file</p>
                  <button onClick={()=>inputRef.current?.click()}
                          className="px-4 py-2 rounded-xl bg-indigo-600 hover:bg-indigo-500">
                    Choose file
                  </button>
                </div>
              </div>
            )}

            {/* Editor */}
            {videoURL && (
              <div className={`mt-4 p-4 ${containerClasses} shadow-2xl ring-1 ring-white/5`}>
                <div className="grid lg:grid-cols-5 gap-4">
                  {/* Player */}
                    <div className="lg:col-span-3">
                      <div ref={playerRef} className="relative aspect-video rounded-xl overflow-hidden bg-black">
                      {/* removed SVG sharpen filter to avoid performance overhead */}
                       <video
                        src={videoURL}
                        ref={videoRef}
                        className="absolute inset-0 w-full h-full object-contain"
                        style={{ filter: videoFilterCSS }}
                        controls
                        onLoadedMetadata={onLoadedMetadata}
                       ></video>
                      {/* Live subtitle overlay */}
                          <div className="subtitle-overlay" style={{ bottom: `${captionStyle.yOffsetPercent}%` }}>
                        {activeWord && (
                          <span
                            className="word-pop rounded px-2 py-1"
                            style={{
                              display:'inline-block',
                              fontFamily: captionStyle.fontFamily,
                              fontWeight: captionStyle.fontWeight,
                              fontSize: `${Math.round(captionStyle.fontSize * (activeWord.emphasized ? (captionStyle.emphasizeScale || 1.35) : 1))}px`,
                              color: activeWord.emphasized ? (captionStyle.emphasizeColor || '#f59e0b') : captionStyle.color,
                                textShadow: (function(){
                                  const s = Math.max(0, Number(captionStyle.shadowSize||0));
                                  if (!s) return 'none';
                                  const c = captionStyle.shadowColor || '#000000';
                                  const b1 = Math.max(1, Math.round(s*0.35));
                                  const b2 = Math.max(2, Math.round(s*0.8));
                                  const b3 = Math.max(3, Math.round(s*1.3));
                                  return [`0 1px ${b1}px ${c}`, `0 0 ${b2}px ${c}`, `0 0 ${b3}px ${c}`].join(', ');
                                })(),
                              ...(function(){
                                const effect = captionStyle.wordEffect || 'none';
                                const onlyEmph = !!captionStyle.effectOnlyEmphasized;
                                const apply = !onlyEmph || !!activeWord.emphasized;
                                if (!apply || effect === 'none') return {};
                                const dur = Math.max(50, Number(captionStyle.effectDurationMs||250)) / 1000;
                                const now = videoRef.current?.currentTime || 0;
                                let p = Math.max(0, Math.min(1, (now - activeWord.start) / dur));
                                // ease-out cubic
                                p = 1 - Math.pow(1 - p, 3);
                                let opacity = 1, scale = 1, ty = 0;
                                switch(effect){
                                  case 'fade': opacity = p; break;
                                  case 'slide': opacity = p; ty = (1-p) * 14; break;
                                  case 'scale': scale = 0.6 + 0.4*p; break;
                                  case 'pop': default: opacity = 0.2 + 0.8*p; scale = 0.85 + 0.15*p; break;
                                }
                                return { opacity, transform: `translateY(${ty}px) scale(${scale})` };
                              })()
                            }}
                          >
                            {captionStyle.uppercase ? activeWord.text.toUpperCase() : activeWord.text}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Timeline moved below the two-column grid */}
                  </div>

                  {/* Right panel */}
                  <div className="lg:col-span-2 space-y-4">
                    {/* Captioning controls removed: auto-caption runs on upload */}

                    {/* Style controls */}
                      <div className="p-3 rounded-xl bg-neutral-900/70 border border-neutral-800 ring-1 ring-white/5">
                      <div className="flex items-center gap-2 mb-2">
                        <button onClick={()=>setStyleTab('style')} className={`px-3 py-1.5 rounded-lg text-sm ${styleTab==='style'?'bg-neutral-800 text-white':'bg-neutral-800/50 text-neutral-300'} border border-neutral-700`}>Style</button>
                        <button onClick={()=>setStyleTab('fx')} className={`px-3 py-1.5 rounded-lg text-sm ${styleTab==='fx'?'bg-neutral-800 text-white':'bg-neutral-800/50 text-neutral-300'} border border-neutral-700`}>Video Effects</button>
                      </div>
                      {styleTab==='style' && (
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <label className="flex items-center gap-2">
                          <input type="checkbox" checked={captionStyle.uppercase}
                                 onChange={(e)=>setCaptionStyle(s=>({...s, uppercase:e.target.checked}))}
                                 className="h-4 w-4 accent-indigo-500"/>
                          <span className="text-xs text-neutral-400">Uppercase</span>
                        </label>
                        <label className="space-y-1">
                          <div className="text-xs text-neutral-400">Word effect</div>
                          <select
                            value={captionStyle.wordEffect}
                            onChange={(e)=>setCaptionStyle(s=>({...s, wordEffect:e.target.value}))}
                            className="w-full bg-neutral-800/80 border border-neutral-700 rounded px-2 py-1"
                          >
                            <option value="none">None</option>
                            <option value="pop">Pop</option>
                            <option value="fade">Fade in</option>
                            <option value="slide">Slide up</option>
                            <option value="scale">Scale in</option>
                          </select>
                        </label>
                        <label className="space-y-1">
                          <div className="flex items-center justify-between text-xs text-neutral-400">
                            <span>Effect duration</span><span className="text-neutral-500">{Math.round(captionStyle.effectDurationMs)}ms</span>
                          </div>
                          <input type="range" min="80" max="800" step="10" value={captionStyle.effectDurationMs}
                                 onChange={(e)=>setCaptionStyle(s=>({...s, effectDurationMs:Number(e.target.value)}))}
                                 className="w-full"/>
                        </label>
                        <label className="flex items-center gap-2">
                          <input type="checkbox" className="h-4 w-4 accent-amber-500" checked={!!captionStyle.effectOnlyEmphasized}
                                 onChange={(e)=>setCaptionStyle(s=>({...s, effectOnlyEmphasized:e.target.checked}))}/>
                          <span className="text-xs text-neutral-400">Apply effect only to emphasized</span>
                        </label>
                        <label className="space-y-1">
                          <div className="text-xs text-neutral-400">Font</div>
                          <select
                            value={captionStyle.fontFamily}
                            onChange={(e)=>setCaptionStyle(s=>({...s, fontFamily:e.target.value}))}
                            className="w-full bg-neutral-800/80 border border-neutral-700 rounded px-2 py-1"
                          >
                            <option value="Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Inter</option>
                            <option value="Poppins, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Poppins</option>
                            <option value="Montserrat, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Montserrat</option>
                            <option value="Oswald, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Oswald</option>
                            <option value="Bebas Neue, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Bebas Neue</option>
                            <option value="Anton, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Anton</option>
                            <option value="Lexend, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Lexend</option>
                            <option value="Outfit, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Outfit</option>
                            <option value="Barlow, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Barlow</option>
                            <option value="Archivo Black, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">Archivo Black</option>
                            <option value="system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif">System UI</option>
                            <option value="Georgia, 'Times New Roman', serif">Serif</option>
                            <option value="ui-monospace, SFMono-Regular, Menlo, Consolas, 'Liberation Mono', monospace">Monospace</option>
                          </select>
                        </label>
                        <label className="space-y-1">
                          <div className="flex items-center justify-between text-xs text-neutral-400">
                            <span>Font size</span><span className="text-neutral-500">{captionStyle.fontSize}px</span>
                          </div>
                          <input type="range" min="14" max="96" value={captionStyle.fontSize}
                                 onChange={(e)=>setCaptionStyle(s=>({...s, fontSize:Number(e.target.value)}))}
                                 className="w-full"/>
                        </label>
                        <label className="space-y-1">
                          <div className="flex items-center justify-between text-xs text-neutral-400">
                            <span>Vertical offset</span><span className="text-neutral-500">{captionStyle.yOffsetPercent}%</span>
                          </div>
                          <input type="range" min="0" max="100" value={captionStyle.yOffsetPercent}
                                 onChange={(e)=>setCaptionStyle(s=>({...s, yOffsetPercent:Number(e.target.value)}))}
                                 className="w-full"/>
                        </label>
                        <label className="space-y-1">
                          <div className="text-xs text-neutral-400">Text color</div>
                          <div className="flex items-center gap-2">
                            <input type="color" value={captionStyle.color}
                                   onChange={(e)=>setCaptionStyle(s=>({...s, color:e.target.value}))}
                                   className="w-8 h-8 p-0 bg-transparent border border-neutral-700 rounded"/>
                            <input type="text" value={captionStyle.color}
                                   onChange={(e)=>{
                                     let v = e.target.value.trim();
                                     if (!v.startsWith('#')) v = '#'+v.replace(/^#+/, '');
                                     v = v.slice(0,7);
                                     setCaptionStyle(s=>({...s, color:v}));
                                   }}
                                    className="flex-1 bg-neutral-800/80 border border-neutral-700 rounded px-2 py-1 text-xs font-mono uppercase"/>
                          </div>
                        </label>
                        <label className="space-y-1">
                          <div className="text-xs text-neutral-400">Emphasis color</div>
                          <div className="flex items-center gap-2">
                            <input type="color" value={captionStyle.emphasizeColor}
                                   onChange={(e)=>setCaptionStyle(s=>({...s, emphasizeColor:e.target.value}))}
                                   className="w-8 h-8 p-0 bg-transparent border border-neutral-700 rounded"/>
                            <input type="text" value={captionStyle.emphasizeColor}
                                   onChange={(e)=>{
                                     let v = e.target.value.trim();
                                     if (!v.startsWith('#')) v = '#'+v.replace(/^#+/, '');
                                     v = v.slice(0,7);
                                     setCaptionStyle(s=>({...s, emphasizeColor:v}));
                                   }}
                                    className="flex-1 bg-neutral-800/80 border border-neutral-700 rounded px-2 py-1 text-xs font-mono uppercase"/>
                          </div>
                        </label>
                        <label className="space-y-1">
                          <div className="text-xs text-neutral-400">Shadow color</div>
                          <div className="flex items-center gap-2">
                            <input type="color" value={captionStyle.shadowColor}
                                   onChange={(e)=>setCaptionStyle(s=>({...s, shadowColor:e.target.value}))}
                                   className="w-8 h-8 p-0 bg-transparent border border-neutral-700 rounded"/>
                            <input type="text" value={captionStyle.shadowColor}
                                   onChange={(e)=>{
                                     let v = e.target.value.trim();
                                     if (!v.startsWith('#')) v = '#'+v.replace(/^#+/, '');
                                     v = v.slice(0,7);
                                     setCaptionStyle(s=>({...s, shadowColor:v}));
                                   }}
                                    className="flex-1 bg-neutral-800/80 border border-neutral-700 rounded px-2 py-1 text-xs font-mono uppercase"/>
                          </div>
                        </label>
                        <label className="space-y-1">
                          <div className="flex items-center justify-between text-xs text-neutral-400">
                            <span>Shadow size</span><span className="text-neutral-500">{Number(captionStyle.shadowSize||0).toFixed(1)}px</span>
                          </div>
                          <input type="range" step="0.1" min="0" max="24" value={captionStyle.shadowSize}
                                 onChange={(e)=>setCaptionStyle(s=>({...s, shadowSize:Number(e.target.value)}))}
                                 className="w-full"/>
                        </label>
                        <label className="space-y-1">
                          <div className="flex items-center justify-between text-xs text-neutral-400">
                            <span>Emphasis scale</span><span className="text-neutral-500">{(captionStyle.emphasizeScale||1).toFixed(2)}x</span>
                          </div>
                          <input type="range" step="0.05" min="1" max="2.5" value={captionStyle.emphasizeScale}
                                 onChange={(e)=>setCaptionStyle(s=>({...s, emphasizeScale:Number(e.target.value)}))}
                                 className="w-full"/>
                        </label>
                      </div>
                      )}
                      {styleTab==='fx' && (
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <label className="space-y-1">
                            <div className="flex items-center justify-between text-xs text-neutral-400"><span>Saturation</span><span className="text-neutral-500">{videoFX.saturate.toFixed(2)}x</span></div>
                            <input type="range" min="0" max="3" step="0.01" value={videoFX.saturate}
                                   onChange={(e)=>setVideoFX(f=>({...f, saturate:Number(e.target.value)}))}/>
                          </label>
                          <label className="space-y-1">
                            <div className="flex items-center justify-between text-xs text-neutral-400"><span>Contrast</span><span className="text-neutral-500">{videoFX.contrast.toFixed(2)}x</span></div>
                            <input type="range" min="0" max="3" step="0.01" value={videoFX.contrast}
                                   onChange={(e)=>setVideoFX(f=>({...f, contrast:Number(e.target.value)}))}/>
                          </label>
                          <label className="space-y-1">
                            <div className="flex items-center justify-between text-xs text-neutral-400"><span>Brightness</span><span className="text-neutral-500">{videoFX.brightness.toFixed(2)}x</span></div>
                            <input type="range" min="0" max="3" step="0.01" value={videoFX.brightness}
                                   onChange={(e)=>setVideoFX(f=>({...f, brightness:Number(e.target.value)}))}/>
                          </label>
                          <label className="space-y-1">
                            <div className="flex items-center justify-between text-xs text-neutral-400"><span>Hue</span><span className="text-neutral-500">{videoFX.hue}°</span></div>
                            <input type="range" min="-180" max="180" step="1" value={videoFX.hue}
                                   onChange={(e)=>setVideoFX(f=>({...f, hue:Number(e.target.value)}))}/>
                          </label>
                          <label className="space-y-1">
                            <div className="flex items-center justify-between text-xs text-neutral-400"><span>Sepia</span><span className="text-neutral-500">{videoFX.sepia.toFixed(2)}</span></div>
                            <input type="range" min="0" max="1" step="0.01" value={videoFX.sepia}
                                   onChange={(e)=>setVideoFX(f=>({...f, sepia:Number(e.target.value)}))}/>
                          </label>
                          <label className="space-y-1">
                            <div className="flex items-center justify-between text-xs text-neutral-400"><span>Grayscale</span><span className="text-neutral-500">{videoFX.grayscale.toFixed(2)}</span></div>
                            <input type="range" min="0" max="1" step="0.01" value={videoFX.grayscale}
                                   onChange={(e)=>setVideoFX(f=>({...f, grayscale:Number(e.target.value)}))}/>
                          </label>
                          <label className="space-y-1">
                            <div className="flex items-center justify-between text-xs text-neutral-400"><span>Invert</span><span className="text-neutral-500">{videoFX.invert.toFixed(2)}</span></div>
                            <input type="range" min="0" max="1" step="0.01" value={videoFX.invert}
                                   onChange={(e)=>setVideoFX(f=>({...f, invert:Number(e.target.value)}))}/>
                          </label>
                          <label className="space-y-1">
                            <div className="flex items-center justify-between text-xs text-neutral-400"><span>Blur</span><span className="text-neutral-500">{videoFX.blur.toFixed(2)}px</span></div>
                            <input type="range" min="0" max="8" step="0.1" value={videoFX.blur}
                                   onChange={(e)=>setVideoFX(f=>({...f, blur:Number(e.target.value)}))}/>
                          </label>
                          <div className="col-span-2 flex justify-end">
                            <button onClick={()=>setVideoFX({saturate:1,contrast:1,brightness:1,hue:0,sepia:0,grayscale:0,invert:0,blur:0})}
                                    className="px-3 py-1.5 rounded-lg bg-neutral-800 hover:bg-neutral-700 text-xs border border-neutral-700">Reset effects</button>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Segment list moved below the two-column grid */}

                  </div>
                </div>
                {/* Full-width timeline below grid */}
                <div className="mt-3">
                  <div className="flex items-center justify-between text-xs text-neutral-400 mb-1">
                    <div className="flex items-center gap-2">
                      <div>Timeline</div>
                      <button onClick={addSegmentAtCurrent}
                              className="px-2 py-1 rounded-lg bg-indigo-600 hover:bg-indigo-500 text-[11px] shadow shadow-indigo-500/20">
                        + Segment
                      </button>
                      <div className="ml-2 flex items-center gap-1">
                        <span className="text-[10px] uppercase tracking-wider text-neutral-500">Words per unit</span>
                        <select
                          value={wordsPerUnit}
                          onChange={(e)=>setWordsPerUnit(Number(e.target.value))}
                          className="px-2 py-1 rounded-lg bg-neutral-900 border border-neutral-700 text-neutral-200"
                        >
                          <option value={1}>1</option>
                          <option value={2}>2</option>
                          <option value={3}>3</option>
                          <option value={4}>4</option>
                          <option value={5}>5</option>
                        </select>
                      </div>
                    </div>
                    <div>{formatTime(videoRef.current?.duration || duration)}</div>
                  </div>
                  <div className="relative overflow-x-auto hide-scrollbar border border-neutral-800 rounded-xl bg-neutral-900/70">
                    <div className="relative min-w-[600px] px-2 py-2 space-y-2" style={{width: totalWidth + 16}}>
                      <div className="text-[10px] uppercase tracking-wider text-neutral-500 px-1">Segments (Auto)</div>
                      <div className="relative h-6">
                        {timelineLayers.base.map(({i, left, width}) => (
                          <button key={`seg-base-${i}`}
                            className={`seg-thumb ${activeIdx===i ? 'active' : ''}`}
                            style={{position:'absolute', left: left+8, width}}
                            title={segments[i]?.text}
                            onClick={()=>jumpTo(i)}
                          />
                        ))}
                      </div>
                      {timelineLayers.added.length > 0 && (
                        <>
                          <div className="text-[10px] uppercase tracking-wider text-neutral-500 px-1">Added</div>
                          <div className="relative h-6">
                            {timelineLayers.added.map(({i, left, width}) => (
                              <button key={`seg-added-${i}`}
                                className={`seg-thumb-added ${activeIdx===i ? 'active' : ''}`}
                                style={{position:'absolute', left: left+8, width}}
                                title={segments[i]?.text}
                                onClick={()=>jumpTo(i)}
                              />
                            ))}
                          </div>
                        </>
                      )}
                      <div className="text-[10px] uppercase tracking-wider text-neutral-500 px-1">Words</div>
                      <div className="relative h-4">
                        {timelineWords.map(({i, left, width}) => (
                          <button key={`w-${i}`}
                            className={`word-thumb ${activeWord && words[i] && activeWord.start===words[i].start ? 'active' : ''}`}
                            style={{position:'absolute', left: left+8, width}}
                            title={words[i]?.text}
                            onClick={()=>jumpToTime(words[i]?.start)}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-neutral-500 mt-1">
                    Click a block to jump. Block width reflects duration.
                  </div>
                </div>

                {/* Full-width segments list below timeline with emphasis controls (compact layout) */}
                <div className="mt-4 p-3 rounded-xl bg-neutral-900 border border-neutral-800">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium">Segments ({segments.length})</div>
                    <div className="flex items-center gap-2">
                      <button className="px-2 py-1 rounded bg-neutral-800 hover:bg-neutral-700 text-xs border border-neutral-700"
                              onClick={()=>{
                                const ids = new Set(selectedSegs);
                                if (!ids.size) return;
                                setSegments(prev => prev.filter((_, idx)=>!ids.has(idx)));
                                setSelectedSegs([]);
                              }}>Delete selected</button>
                      <div className="text-xs text-neutral-500">Editable / Emphasis</div>
                    </div>
                  </div>
                  <div className="space-y-2 max-h-[50vh] overflow-auto">
                    {segments.map((s, i)=>{
                      const tokens = (s.text||'').trim().length ? (s.text||'').trim().split(/\s+/) : [];
                      const emphasizedSet = new Set(s.emphasizedWordIndices||[]);
                      return (
                        <div key={i} className={`p-2 rounded-lg ${activeIdx===i?'bg-neutral-800':'bg-neutral-900'} border border-neutral-800`}>
                          <div className="flex items-center gap-2 text-[11px] text-neutral-400 mb-1">
                            <input type="checkbox" className="mr-1 accent-indigo-500" checked={selectedSegs.includes(i)}
                                   onChange={(e)=>{
                                     setSelectedSegs(prev=>{
                                       const set = new Set(prev);
                                       if (e.target.checked) set.add(i); else set.delete(i);
                                       return Array.from(set).sort((a,b)=>a-b);
                                     });
                                   }}/>
                            <label className="flex items-center gap-1">
                              <span>Start</span>
                              <input
                                className={`input-num px-2 py-0.5 rounded font-mono w-[130px] bg-neutral-800 border ${(()=>{ const draft = Object.prototype.hasOwnProperty.call(startDrafts, i)? startDrafts[i] : formatTime(s.start); const v = parseTimeToSeconds(draft); const invalid = !Number.isFinite(v) || v < 0 || v > (s.end - 0.01); return invalid ? 'border-rose-500' : 'border-neutral-700'; })()}`}
                                title="Format: h:mm:ss.mmm or m:ss.mmm. Must be < End."
                                value={Object.prototype.hasOwnProperty.call(startDrafts, i) ? startDrafts[i] : formatTime(s.start)}
                                onFocus={()=>{
                                  setStartDrafts(prev=>({ ...prev, [i]: formatTime(s.start) }));
                                }}
                                onChange={(e)=>{
                                  const text = e.target.value;
                                  setStartDrafts(prev=>({ ...prev, [i]: text }));
                                }}
                                onKeyDown={(e)=>{
                                  if (e.key === 'Enter') {
                                    const text = (startDrafts[i] ?? formatTime(s.start));
                                    const v = parseTimeToSeconds(text);
                                    if (!Number.isFinite(v)) return; // keep focus until valid
                                    const fixed = Math.max(0, Math.min((s.end-0.01), v));
                                    updateSegment(i,{start: fixed});
                                    setStartDrafts(prev=>{ const n={...prev}; delete n[i]; return n; });
                                  } else if (e.key === 'Escape') {
                                    setStartDrafts(prev=>{ const n={...prev}; delete n[i]; return n; });
                                  }
                                }}
                                onBlur={()=>{
                                  const text = (startDrafts[i] ?? formatTime(s.start));
                                  const v = parseTimeToSeconds(text);
                                  if (Number.isFinite(v)) {
                                    const fixed = Math.max(0, Math.min((s.end-0.01), v));
                                    updateSegment(i,{start: fixed});
                                  }
                                  setStartDrafts(prev=>{ const n={...prev}; delete n[i]; return n; });
                                }}
                              />
                              <div className="flex items-center ml-1">
                                <button className="px-1.5 py-0.5 rounded bg-neutral-800 hover:bg-neutral-700 border border-neutral-700"
                                        onClick={()=>updateSegment(i,{start: Math.max(0,(s.start-0.1))})}>-</button>
                                <button className="ml-1 px-1.5 py-0.5 rounded bg-neutral-800 hover:bg-neutral-700 border border-neutral-700"
                                        onClick={()=>updateSegment(i,{start: Math.min(s.end-0.05,(s.start+0.1))})}>+</button>
                              </div>
                            </label>
                            <label className="flex items-center gap-1">
                              <span>End</span>
                              <input
                                className={`input-num px-2 py-0.5 rounded font-mono w-[130px] bg-neutral-800 border ${(()=>{ const draft = Object.prototype.hasOwnProperty.call(endDrafts, i)? endDrafts[i] : formatTime(s.end); const v = parseTimeToSeconds(draft); const maxDur = videoRef.current?.duration || Infinity; const invalid = !Number.isFinite(v) || v < (s.start + 0.01) || v > maxDur; return invalid ? 'border-rose-500' : 'border-neutral-700'; })()}`}
                                title="Format: h:mm:ss.mmm or m:ss.mmm. Must be ≥ Start and ≤ video length."
                                value={Object.prototype.hasOwnProperty.call(endDrafts, i) ? endDrafts[i] : formatTime(s.end)}
                                onFocus={()=>{
                                  setEndDrafts(prev=>({ ...prev, [i]: formatTime(s.end) }));
                                }}
                                onChange={(e)=>{
                                  const text = e.target.value;
                                  setEndDrafts(prev=>({ ...prev, [i]: text }));
                                }}
                                onKeyDown={(e)=>{
                                  if (e.key === 'Enter') {
                                    const text = (endDrafts[i] ?? formatTime(s.end));
                                    const v = parseTimeToSeconds(text);
                                    if (!Number.isFinite(v)) return; // keep focus until valid
                                    const maxDur = videoRef.current?.duration || Infinity;
                                    const fixed = Math.max((s.start+0.01), Math.min(maxDur, v));
                                    updateSegment(i,{end: fixed});
                                    setEndDrafts(prev=>{ const n={...prev}; delete n[i]; return n; });
                                  } else if (e.key === 'Escape') {
                                    setEndDrafts(prev=>{ const n={...prev}; delete n[i]; return n; });
                                  }
                                }}
                                onBlur={()=>{
                                  const text = (endDrafts[i] ?? formatTime(s.end));
                                  const v = parseTimeToSeconds(text);
                                  const maxDur = videoRef.current?.duration || Infinity;
                                  if (Number.isFinite(v)) {
                                    const fixed = Math.max((s.start+0.01), Math.min(maxDur, v));
                                    updateSegment(i,{end: fixed});
                                  }
                                  setEndDrafts(prev=>{ const n={...prev}; delete n[i]; return n; });
                                }}
                              />
                              <div className="flex items-center ml-1">
                                <button className="px-1.5 py-0.5 rounded bg-neutral-800 hover:bg-neutral-700 border border-neutral-700"
                                        onClick={()=>updateSegment(i,{end: Math.max(s.start+0.05,(s.end-0.1))})}>-</button>
                                <button className="ml-1 px-1.5 py-0.5 rounded bg-neutral-800 hover:bg-neutral-700 border border-neutral-700"
                                        onClick={()=>updateSegment(i,{end: (s.end+0.1)})}>+</button>
                              </div>
                            </label>
                            <label className="flex items-center gap-1 ml-2">
                              <input type="checkbox" className="accent-amber-500"
                                     checked={!!s.emphasizeSegment}
                                     onChange={(e)=>updateSegment(i,{emphasizeSegment: e.target.checked})}/>
                              <span>Emphasize</span>
                            </label>
                            <div className="ml-auto flex items-center gap-2">
                              <button className="px-2 py-1 rounded bg-neutral-700 hover:bg-neutral-600 text-xs"
                                      onClick={()=>jumpTo(i)}>Jump</button>
                              <button className="px-2 py-1 rounded bg-rose-600 hover:bg-rose-500 text-xs"
                                      onClick={()=>deleteSegment(i)}>Del</button>
                            </div>
                          </div>
                          <textarea rows={2}
                                    className="w-full px-2 py-1 rounded bg-neutral-800 border border-neutral-700 resize-y"
                                    value={s.text}
                                    onChange={(e)=>{
                                      const newText = e.target.value;
                                      const oldTokens = (s.text||'').trim()? (s.text||'').trim().split(/\s+/) : [];
                                      const newTokens = (newText||'').trim()? newText.trim().split(/\s+/) : [];
                                      if (oldTokens.length !== newTokens.length) {
                                        const segDur = Math.max(0.001, (s.end - s.start));
                                        updateSegment(i,{text:newText, wordDurations: newTokens.length? newTokens.map(()=>segDur/Math.max(1,newTokens.length)) : []});
                                      } else {
                                        updateSegment(i,{text:newText});
                                      }
                                    }}/>
                          {!!tokens.length && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {tokens.map((tok, wi)=>{
                                const on = emphasizedSet.has(wi);
                                return (
                                  <div key={wi} className="inline-block">
                                    {wordEdit && wordEdit.segIndex===i && wordEdit.wordIndex===wi ? (
                                      <input
                                        autoFocus
                                        type="text"
                                        value={wordEdit.value}
                                        onChange={(e)=>setWordEdit(prev=>({...prev, value:e.target.value}))}
                                        onBlur={()=>{
                                          const newVal = (wordEdit.value||'').trim();
                                          setSegments(prev => prev.map((seg, idx)=>{
                                            if (idx !== i) return seg;
                                            const arr = (seg.text||'').trim().split(/\s+/);
                                            arr[wi] = newVal;
                                            return {...seg, text: arr.join(' ')};
                                          }));
                                          setWordEdit(null);
                                        }}
                                        onKeyDown={(e)=>{
                                          if (e.key==='Enter') { e.currentTarget.blur(); }
                                          else if (e.key==='Escape') { setWordEdit(null); }
                                        }}
                                        className={`px-2 py-0.5 rounded border text-[11px] bg-neutral-800 border-neutral-700 text-neutral-200`}
                                      />
                                    ) : (
                                      <span className="inline-flex items-center">
                                        <button
                                          onClick={(e)=>{
                                            if (e.altKey) {
                                              setWordEdit({ segIndex:i, wordIndex:wi, value: tok });
                                              return;
                                            }
                                            setSegments(prev => prev.map((seg, idx)=>{
                                              if (idx !== i) return seg;
                                              const set = new Set(seg.emphasizedWordIndices||[]);
                                              if (set.has(wi)) set.delete(wi); else set.add(wi);
                                              return {...seg, emphasizedWordIndices: Array.from(set).sort((a,b)=>a-b)};
                                            }));
                                          }}
                                          title="Click to toggle emphasis. Alt+Click to edit word."
                                          className={`px-2 py-0.5 rounded border text-[11px] ${on? 'bg-amber-500/20 border-amber-500 text-amber-300':'bg-neutral-800 border-neutral-700 text-neutral-300'}`}
                                        >{tok}</button>
                                        <div className="inline-flex items-center gap-1 ml-1 align-middle">
                                          <button
                                            className="px-1 py-0.5 rounded bg-neutral-800 hover:bg-neutral-700 border border-neutral-700 text-[10px]"
                                            title="Shorten this word"
                                            onClick={()=>adjustWordDuration(i, wi, -0.1)}
                                          >-</button>
                                          <button
                                            className="px-1 py-0.5 rounded bg-neutral-800 hover:bg-neutral-700 border border-neutral-700 text-[10px]"
                                            title="Lengthen this word"
                                            onClick={()=>adjustWordDuration(i, wi, 0.1)}
                                          >+</button>
                                        </div>
                                      </span>
                                    )}
                                  </div>
                                );
                              })}
                            </div>
                          )}
                        </div>
                      );
                    })}
                    {!segments.length && (
                      <div className="text-neutral-500 text-sm p-4 text-center">
                        Captions will be generated automatically after upload. If nothing appears, click "Generate" above.
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </main>

          {/* Modal overlay during processing */}
          {/* Language confirmation prompt */}
          {showLangConfirm && videoURL && (
            <div className="fixed inset-0 z-50 grid place-items-center bg-black/50">
              <div className="px-4 py-3 rounded-xl bg-neutral-900 border border-neutral-800 shadow-xl text-sm text-neutral-200 max-w-md w-[460px]">
                <div className="font-semibold mb-1">Detected language</div>
                <div className="text-neutral-400 mb-3">We can auto‑detect the spoken language. You can change it below if needed. Or skip transcription to add your own segments and text manually (no credits used).</div>
                <div className="flex items-center gap-2">
                  <label className="text-xs text-neutral-400">Language</label>
                  <select
                    value={languageChoice}
                    onChange={(e)=>setLanguageChoice(e.target.value)}
                    className="flex-1 px-2 py-1.5 rounded-lg bg-neutral-800/60 border border-neutral-700"
                  >
                    <option value="auto">Auto‑detect</option>
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                    <option value="it">Italian</option>
                    <option value="pt">Portuguese</option>
                    <option value="nl">Dutch</option>
                    <option value="pl">Polish</option>
                    <option value="ru">Russian</option>
                    <option value="tr">Turkish</option>
                    <option value="uk">Ukrainian</option>
                    <option value="ar">Arabic</option>
                    <option value="hi">Hindi</option>
                    <option value="ja">Japanese</option>
                    <option value="ko">Korean</option>
                    <option value="zh">Chinese</option>
                    <option value="vi">Vietnamese</option>
                    <option value="sv">Swedish</option>
                    <option value="no">Norwegian Bokmål</option>
                    <option value="da">Danish</option>
                    <option value="fi">Finnish</option>
                  </select>
                </div>
                <div className="mt-3 flex justify-between gap-2">
                  <button className="px-3 py-1.5 rounded-lg bg-neutral-800 hover:bg-neutral-700 border border-neutral-700" onClick={()=>setShowLangConfirm(false)}>Cancel</button>
                  <div className="flex gap-2">
                    <button className="px-3 py-1.5 rounded-lg bg-neutral-800 hover:bg-neutral-700 border border-neutral-700" onClick={()=>{ setShowLangConfirm(false); /* Skip transcription */ }}>Skip</button>
                    <button className="px-3 py-1.5 rounded-lg bg-indigo-600 hover:bg-indigo-500 font-semibold" onClick={()=>{ setShowLangConfirm(false); handleGenerate(); }}>Continue</button>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Modal overlay during processing */}
          {isProcessing && (
            <div className="fixed inset-0 z-50 grid place-items-center bg-black/50">
              <div className="px-4 py-3 rounded-xl bg-neutral-900 border border-neutral-800 shadow-xl text-sm text-neutral-200 max-w-md text-center">
                <div className="font-semibold mb-1">
                  {processingMode === 'export' ? (
                    processingError ? 'Export failed' : 'Exporting…'
                  ) : (
                    processingError ? 'Transcription failed' : 'Transcribing…'
                  )}
                </div>
                <div className="text-neutral-400">
                  {processingError ? (
                    <span className="text-rose-400">{processingError}</span>
                  ) : (
                    processingMode === 'export' ? 'Rendering and encoding your video. This may take a few minutes.' : 'Uploading to server and processing securely. Processing time scales with video length.'
                  )}
                </div>
              </div>
            </div>
          )}

          <footer className="mx-auto max-w-7xl px-4 py-8 text-neutral-500 text-sm">
            Built for demo purposes. Replace the mocked transcriber with your API and you're off to the races.
          </footer>
        </div>
      );
    }

    // Canvas export with baked-in per-word captions
    async function exportBakedVideo(options = {}) {
      // Access component state via DOM nodes/refs
      const v = document.querySelector('video');
      if (!v) { alert('No video loaded'); return; }
      const app = window.__CAPTIONER_APP_STATE__;
      const segments = app?.getSegments?.() || [];
      const style = app?.getCaptionStyle?.() || {
        fontFamily: 'Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif',
        fontWeight: 800,
        fontSize: 36,
        yOffsetPercent: 40,
        color: '#ffffff',
        shadowColor: '#000000',
        shadowSize: 6,
        uppercase: true,
        emphasizeColor: '#f59e0b',
        emphasizeScale: 1.35,
        wordEffect: 'pop',
        effectOnlyEmphasized: false,
        effectDurationMs: 250,
      };
      const fx = app?.getVideoFX?.() || { saturate:1, contrast:1, brightness:1, hue:0, sepia:0, grayscale:0, invert:0, blur:0 };
      const file = window.__CAPTIONER_APP_STATE__?.getVideoFile?.();
      // Build words from segments, respecting per-word durations
      const words = (()=>{
        const result = [];
        for (const seg of segments) {
          const text = (seg.text || '').trim();
          if (!text) continue;
          const tokens = text.split(/\s+/);
          const segDur = Math.max(0.001, (seg.end ?? seg.start) - seg.start);
          let durs = (Array.isArray(seg.wordDurations) && seg.wordDurations.length === tokens.length)
            ? seg.wordDurations.slice()
            : tokens.map(()=>segDur/Math.max(1,tokens.length));
          const sum = durs.reduce((a,b)=>a+b,0);
          const scale = sum>0 ? (segDur / sum) : 1;
          durs = durs.map(d=>d*scale);
          let t = seg.start;
          tokens.forEach((token, wordIndex) => {
            const start = t;
            let end = Math.min(seg.end, t + durs[wordIndex]);
            if (wordIndex === tokens.length - 1) end = Math.min(v.duration || Infinity, end + 0.04);
            const emphasized = Boolean(seg.emphasizeSegment) || Boolean((seg.emphasizedWordIndices||[]).includes(wordIndex));
            result.push({ text: token, start, end, emphasized });
            t = end;
          });
        }
        return result;
      })();

      // Prepare canvas & drawing
      await v.play().catch(()=>{});
      v.pause();
      await new Promise(r=>setTimeout(r, 50));
      const width = v.videoWidth || 1280;
      const height = v.videoHeight || 720;
      // Derive FPS from source if possible
      let fps = await estimateVideoFps(v).catch(()=>null);
      fps = Math.min(60, Math.max(15, Math.round(fps || 30)));
      const canvas = Object.assign(document.createElement('canvas'), { width, height });
      const ctx = canvas.getContext('2d');
      const canvasStream = canvas.captureStream(fps);

      // Try to capture audio from the video element
      let mixedStream = canvasStream;
      try {
        const vidStream = v.captureStream();
        const audioTrack = vidStream.getAudioTracks()[0];
        if (audioTrack) {
          mixedStream = new MediaStream([canvasStream.getVideoTracks()[0], audioTrack]);
        }
      } catch (e) {}

      const ext = (file?.name?.split('.').pop() || '').toLowerCase();
      const preferMime = file?.type || '';
      let mime = preferMime && MediaRecorder.isTypeSupported(preferMime) ? preferMime : '';
      if (!mime) {
        const candidates = [
          'video/mp4;codecs=avc1,mp4a.40.2', 'video/mp4', 'video/webm;codecs=vp9,opus', 'video/webm;codecs=vp8,opus', 'video/webm'
        ];
        mime = candidates.find(m => MediaRecorder.isTypeSupported(m)) || '';
      }
      // Approximate source bitrate from file size and duration
      const approxVbps = (file && v.duration) ? Math.max(100000, Math.round((file.size * 8) / v.duration)) : undefined;
      const recOpts = mime ? { mimeType: mime } : {};
      if (approxVbps) {
        recOpts.videoBitsPerSecond = approxVbps;
        recOpts.audioBitsPerSecond = 128000;
      }
      let recorder;
      try { recorder = new MediaRecorder(mixedStream, recOpts); }
      catch(e) { try { recorder = new MediaRecorder(mixedStream); } catch(err) { alert('Recording not supported in this browser.'); return; } }
      const chunks = [];
      recorder.ondataavailable = (e)=>{ if (e.data && e.data.size) chunks.push(e.data); };
      recorder.onerror = (e)=>console.error('Recorder error', e);
      const done = new Promise(res=>recorder.onstop = res);
      recorder.start(Math.round(1000/Math.max(1,fps)));

      // Play and draw
      v.currentTime = 0;
      await v.play().catch(()=>{});
      const startEpoch = performance.now();
      let stopped = false;
      const stopRecording = () => { if (stopped) return; stopped = true; try { recorder.stop(); } catch(e){} };
      const draw = () => {
        if (v.paused || v.ended) return;
        ctx.clearRect(0,0,width,height);
        // Apply CSS-like filters via canvas filter property
        const filt = [
          `saturate(${fx.saturate||1})`,
          `contrast(${fx.contrast||1})`,
          `brightness(${fx.brightness||1})`,
          `hue-rotate(${fx.hue||0}deg)`,
          `sepia(${fx.sepia||0})`,
          `grayscale(${fx.grayscale||0})`,
          `invert(${fx.invert||0})`,
          `blur(${fx.blur||0}px)`
        ].join(' ');
        ctx.filter = filt;
        ctx.drawImage(v, 0, 0, width, height);
        ctx.filter = 'none';
        // draw current word
        const t = v.currentTime;
        const w = words.find(w => t >= w.start && t < (w.end + 0.02));
        if (w) {
          const apply = !style.effectOnlyEmphasized || !!w.emphasized;
          const dispStyle = w.emphasized
            ? { ...style, color: style.emphasizeColor || style.color, fontSize: Math.round((style.fontSize||36) * (style.emphasizeScale || 1.35)) }
            : style;
          const dur = Math.max(50, Number(style.effectDurationMs||250)) / 1000;
          let p = Math.max(0, Math.min(1, (v.currentTime - w.start) / dur));
          p = 1 - Math.pow(1 - p, 3);
          let scale = 1;
          if (apply) {
            switch(style.wordEffect){
              case 'scale': scale = 0.6 + 0.4*p; break;
              case 'pop': default: scale = 0.85 + 0.15*p; break;
            }
          }
          drawWord(ctx, style.uppercase ? w.text.toUpperCase() : w.text, width, height, dispStyle, scale);
        }
        // Watermark overlay when requested
        if (options.watermark) {
          ctx.save();
          const wmText = 'TRIAL EXPORT — Upgrade for no watermark ($9/mo)';
          ctx.globalAlpha = 0.85;
          ctx.font = `800 ${Math.round(Math.max(18, width * 0.03))}px Inter, sans-serif`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'bottom';
          const metrics = ctx.measureText(wmText);
          const padX = 18, padY = 10;
          const textW = metrics.width;
          const boxW = textW + padX*2;
          const boxH = Math.round((width * 0.03) + padY*2);
          const x = Math.round(width/2);
          const y = height - Math.round(height * 0.06);
          ctx.fillStyle = 'rgba(0,0,0,0.35)';
          ctx.fillRect(x - boxW/2, y - boxH + 6, boxW, boxH);
          ctx.fillStyle = 'rgba(255,255,255,0.95)';
          ctx.fillText(wmText, x, y);
          ctx.restore();
        }
        if (v.currentTime >= (v.duration - 0.001)) { stopRecording(); return; }
        requestAnimationFrame(draw);
      };
      draw();
      v.onended = stopRecording;
      await done;
      if (!chunks.length) { alert('No data was recorded. Try changing the export format or browser.'); return; }
      const blob = new Blob(chunks, { type: mime || 'video/webm' });
      const a = document.createElement('a');
      const url = URL.createObjectURL(blob);
      const outExt = (mime.includes('mp4') || ext === 'mp4') ? 'mp4' : (mime.includes('webm') ? 'webm' : ext || 'webm');
      a.href = url; a.download = `captions_baked.${outExt}`; a.click();
      setTimeout(()=>URL.revokeObjectURL(url), 2000);
    }

    function drawWord(ctx, text, width, height, style, scaleOverride) {
      ctx.save();
      const fontPx = Math.max(8, Number(style.fontSize || 36));
      const font = `${style.fontWeight || 800} ${fontPx}px ${style.fontFamily || 'Inter, system-ui, sans-serif'}`;
      ctx.font = font;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'alphabetic';
      const y = height - (height * (Number(style.yOffsetPercent || 6) / 100));
      ctx.translate(width/2, y);
      if (Number.isFinite(scaleOverride) && scaleOverride !== 1) {
        ctx.scale(scaleOverride, scaleOverride);
      }
      // Shadow-based readability instead of outline
      const shadowSize = Math.max(0, Number(style.shadowSize || 0));
      if (shadowSize > 0) {
        ctx.shadowColor = style.shadowColor || '#000000';
        ctx.shadowBlur = shadowSize;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = Math.max(1, Math.round(shadowSize * 0.2));
      } else {
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
      }
      // Fill
      ctx.fillStyle = style.color || '#ffffff';
      ctx.fillText(text, 0, 0);
      ctx.restore();
    }

    ReactDOM.createRoot(document.getElementById('root')).render(<MainApp/>);
  </script>
</body>
</html>
