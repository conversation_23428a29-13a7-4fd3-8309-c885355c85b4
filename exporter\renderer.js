import fs from 'node:fs';
import fsp from 'node:fs/promises';
import path from 'node:path';
import { createCanvas, registerFont } from 'canvas';

// Track which fonts are registered so we can enforce no fallbacks
const REGISTERED_FAMILIES = new Set();
const REGISTERED_KEYS = new Set(); // family:weight
function markRegistered(family, weight) {
  if (!family) return;
  try { REGISTERED_FAMILIES.add(family); REGISTERED_KEYS.add(`${family}:${String(weight||'')}`); } catch {}
}


// Register Inter family in multiple weights
function registerInterWeights() {
  const faces = [
    { p: '/usr/share/fonts/truetype/inter/Inter-Regular.ttf', opts: { family: 'Inter', weight: '400' } },
    { p: '/usr/share/fonts/truetype/inter/Inter-Medium.ttf', opts: { family: 'Inter', weight: '500' } },
    { p: '/usr/share/fonts/truetype/inter/Inter-SemiBold.ttf', opts: { family: 'Inter', weight: '600' } },
    { p: '/usr/share/fonts/truetype/inter/Inter-Bold.ttf', opts: { family: 'Inter', weight: '700' } },
    { p: '/usr/share/fonts/truetype/inter/Inter-ExtraBold.ttf', opts: { family: 'Inter', weight: '800' } },
    { p: '/usr/share/fonts/truetype/inter/Inter-Black.ttf', opts: { family: 'Inter', weight: '900' } },
    { p: '/usr/share/fonts/truetype/inter/InterVariable.ttf', opts: { family: 'Inter' } },
  ];
  for (const { p, opts } of faces) {
    try {
      if (fs.existsSync(p)) {
        registerFont(p, opts);
        markRegistered(opts.family, opts.weight);
      }
    } catch (e) {
      // Silent fail
    }
  }
}
registerInterWeights();

// Register editor fonts directly from Docker paths
function registerEditorFonts() {
  const fonts = [
    { path: '/usr/share/fonts/truetype/custom/Poppins-ExtraBold.ttf', family: 'Poppins', weight: '800' },
    { path: '/usr/share/fonts/truetype/custom/Montserrat-ExtraBold.ttf', family: 'Montserrat', weight: '800' },
    { path: '/usr/share/fonts/truetype/custom/Oswald-Bold.ttf', family: 'Oswald', weight: '700' },
    { path: '/usr/share/fonts/truetype/custom/BebasNeue-Regular.ttf', family: 'Bebas Neue', weight: '400' },
    { path: '/usr/share/fonts/truetype/custom/Anton-Regular.ttf', family: 'Anton', weight: '400' },
    { path: '/usr/share/fonts/truetype/custom/Lexend-ExtraBold.ttf', family: 'Lexend', weight: '800' },
    { path: '/usr/share/fonts/truetype/custom/Outfit-ExtraBold.ttf', family: 'Outfit', weight: '800' },
    { path: '/usr/share/fonts/truetype/custom/Barlow-ExtraBold.ttf', family: 'Barlow', weight: '800' },
    { path: '/usr/share/fonts/truetype/custom/ArchivoBlack-Regular.ttf', family: 'Archivo Black', weight: '900' }
  ];

  for (const font of fonts) {
    try {
      if (fs.existsSync(font.path)) {
        registerFont(font.path, { family: font.family, weight: font.weight });
        markRegistered(font.family, font.weight);
      }
    } catch (e) {
      // Silent fail
    }
  }
}

// Register fonts from local project folders and common OS locations
function registerLocalFonts() {
  const roots = [
    path.join(process.cwd(), 'exporter', 'fonts'),
    path.join(process.cwd(), 'fonts'),
    '/usr/share/fonts/truetype/custom',
    '/usr/share/fonts/truetype',
    '/usr/local/share/fonts',
    process.platform === 'win32' ? 'C:\\Windows\\Fonts' : null,
  ].filter(Boolean);

  const weightFromName = (name) => {
    const n = String(name).toLowerCase();
    if (n.includes('black')) return '900';
    if (n.includes('extrabold') || n.includes('extra-bold')) return '800';
    if (n.includes('semibold') || n.includes('semi-bold')) return '600';
    if (n.includes('bold')) return '700';
    if (n.includes('medium')) return '500';
    return '400';
  };

  const normalizeFamily = (s) => {
    const k = String(s||'').toLowerCase().replace(/[_-]+/g,' ').trim();
    const map = {
      archivoblack: 'Archivo Black',
      'archivo black': 'Archivo Black',
      bebasneue: 'Bebas Neue',
      'bebas neue': 'Bebas Neue',
      bebas: 'Bebas Neue',
    };
    if (map[k]) return map[k];
    return k.replace(/\b\w/g, ch => ch.toUpperCase());
  };

  const walk = (dir) => {
    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });
      for (const ent of entries) {
        const p = path.join(dir, ent.name);
        if (ent.isDirectory()) { walk(p); continue; }
        if (!/\.(ttf|otf)$/i.test(ent.name)) continue; // node-canvas supports only TTF/OTF

        const base = ent.name.replace(/\.(ttf|otf)$/i, '');
        // Prefer the part before the first dash as family, else fallback to parent folder name
        let family = base.split('-')[0] || path.basename(path.dirname(p));
        family = normalizeFamily(family);
        const weight = weightFromName(base);
        try {
          registerFont(p, { family, weight });
          markRegistered(family, weight);

// Resolve the primary family from the CSS stack (first token, sans quotes)
function primaryFamilyOf(style){
  const stack = String(style?.fontFamily || '').split(',').map(s=>s.trim().replace(/^['"]|['"]$/g,''));
  return stack[0] || '';
}

function isGenericFamily(f){
  const g = String(f).toLowerCase();
  return g === 'system-ui' || g === '-apple-system' || g === 'segoe ui' || g === 'ui-monospace' || g === 'monospace' || g === 'serif' || g === 'georgia';
}

function resolveGenericFamily(f){
  const g = String(f).toLowerCase();
  if (g === 'ui-monospace' || g === 'monospace') return 'DejaVu Sans Mono';
  if (g === 'serif' || g === 'georgia') return 'DejaVu Serif';
  return 'Inter'; // system-ui / -apple-system / Segoe UI
}

function ensureSelectedFontAvailable(style){
  const wanted = primaryFamilyOf(style);
  if (!wanted) throw new Error('font_family_missing');
  if (isGenericFamily(wanted)) return resolveGenericFamily(wanted);
  // Non-generic: must be registered
  const weight = String(style?.fontWeight || '800');
  const exact = REGISTERED_KEYS.has(`${wanted}:${weight}`);
  const any = REGISTERED_FAMILIES.has(wanted);
  if (!exact && !any) {
    const available = Array.from(REGISTERED_FAMILIES).sort();
    throw new Error(`font_not_installed:${wanted};available=${available.join('|')}`);
  }
  return wanted;
}

        } catch {}
      }
    } catch {}
  };

  for (const root of roots) {
    if (fs.existsSync(root)) walk(root);
  }
}
registerLocalFonts();

registerEditorFonts();

function easeOutCubic(p) { return 1 - Math.pow(1 - p, 3); }

function pickActiveWord(t, segments) {
  for (const seg of segments) {
    if (t >= seg.start && t < seg.end + 0.02) {
      const text = String(seg.text || '').trim();
      if (!text) return null;
      const tokens = text.split(/\s+/);
      const segDur = Math.max(0.001, seg.end - seg.start);
      let durs = Array.isArray(seg.wordDurations) && seg.wordDurations.length === tokens.length
        ? seg.wordDurations.slice()
        : tokens.map(()=> segDur / Math.max(1, tokens.length));
      const sum = durs.reduce((a,b)=>a+b,0) || 1;
      const scale = segDur / sum;
      durs = durs.map(d => d * scale);
      let tt = seg.start;
      for (let i=0;i<tokens.length;i++) {
        const start = tt;
        const end = i === tokens.length - 1 ? Math.min(seg.end + 0.04, tt + durs[i]) : Math.min(seg.end, tt + durs[i]);
        if (t >= start && t < end + 0.02) {

	  // Validate and lock the font to a single concrete family (no fallback)
	  baseStyle.fontFamily = ensureSelectedFontAvailable(baseStyle);

          const emphasized = Boolean(seg.emphasizeSegment) || Boolean((seg.emphasizedWordIndices||[]).includes(i));
          return { text: tokens[i], start, end, emphasized };
        }
        tt = end;
      }
      return null;
    }
  }
  return null;
}

export async function renderOverlayFrames({ framesDir, width, height, fps, segments, style, watermark, videoDuration }) {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');
  // Use provided video duration, or fallback to segments duration with extra padding
  const segmentDuration = Math.max(0, segments.reduce((m,s)=> Math.max(m, s.end||0), 0));
  const duration = videoDuration || segmentDuration + 1.0; // Add 1 second padding if no videoDuration
  const totalFrames = Math.max(1, Math.round(duration * fps));


  const baseStyle = Object.assign({
    fontFamily: 'Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif',
    fontWeight: 800,
    fontSize: 36,
    yOffsetPercent: 40,
    color: '#ffffff',
    shadowColor: '#000000',
    shadowSize: 6,
    uppercase: true,
    emphasizeColor: '#f59e0b',
    emphasizeScale: 1.35,
    wordEffect: 'pop',
    effectOnlyEmphasized: false,
    effectDurationMs: 250
  }, style || {});

  // Lock to a single, registered family or throw (no fallback rendering)
  baseStyle.fontFamily = ensureSelectedFontAvailable(baseStyle);

  // Apply font scaling based on video resolution
  // Base resolution is 720p, so scale font size accordingly
  // For 1080p: 1080/720 = 1.5x, For 720p: 720/720 = 1.0x, For vertical 1080x1920: 1920/720 = 2.67x
  const baseResolution = 720;
  const scaleFactor = Math.max(width, height) / baseResolution;
  const scaledFontSize = Math.round(Number(baseStyle.fontSize || 36) * scaleFactor);
  baseStyle.fontSize = scaledFontSize;



  for (let frame = 0; frame <= totalFrames; frame++) {
    const t = frame / fps;
    ctx.clearRect(0,0,width,height);
    ctx.globalCompositeOperation = 'source-over';
    const w = pickActiveWord(t, segments);
    if (w) {
      const apply = !baseStyle.effectOnlyEmphasized || !!w.emphasized;
      const disp = Object.assign({}, baseStyle);
      if (w.emphasized) {
        disp.color = baseStyle.emphasizeColor || baseStyle.color;
        disp.fontSize = Math.round(Number(baseStyle.fontSize) * Number(baseStyle.emphasizeScale||1.35));
      }
      const dur = Math.max(50, Number(baseStyle.effectDurationMs||250)) / 1000;
      let p = Math.max(0, Math.min(1, (t - w.start) / dur));
      p = easeOutCubic(p);
      let scale = 1, opacity = 1, ty = 0;
      if (apply) {
        switch (String(baseStyle.wordEffect||'pop')) {
          case 'fade': opacity = p; break;
          case 'slide': opacity = p; ty = (1 - p) * 14; break;
          case 'scale': scale = 0.6 + 0.4 * p; break;
          case 'pop': default: opacity = 1; scale = 0.85 + 0.15 * p; break; // Keep opacity at 1 for full color
        }
      }
      drawWord(ctx, baseStyle.uppercase ? String(w.text).toUpperCase() : String(w.text), width, height, disp, { scale, opacity, translateY: ty });
    }

    if (watermark) {
      drawWatermark(ctx, width, height);
    }

    const pth = path.join(framesDir, String(frame).padStart(6,'0') + '.png');
    // Use low compression PNG for speed
    const buf = canvas.toBuffer('image/png', { compressionLevel: 0 });
    await fsp.writeFile(pth, buf);
  }
}

function drawWord(ctx, text, width, height, style, opts) {
  ctx.save();
  const fontPx = Math.max(8, Number(style.fontSize || 36));

  // Use the first font from the CSS font stack - don't force mapping to known families
  const fontStack = String(style.fontFamily || 'Inter').split(',').map(f => f.trim().replace(/^['"]|['"]$/g,''));
  let family = fontStack[0] || 'Inter'; // Use the first font directly

  // Only map system generics to specific fonts
  if (family === 'system-ui' || family === '-apple-system' || family === 'Segoe UI') {
    family = 'Inter';
  } else if (family === 'ui-monospace' || family === 'monospace') {
    family = 'DejaVu Sans Mono';
  } else if (family === 'Georgia' || family.toLowerCase() === 'serif') {
    family = 'DejaVu Serif';
  }
  // For everything else (Poppins, Montserrat, DM Sans, Lato, etc.), use as-is

  const weight = String(style.fontWeight || 800);

  // Simple fallback - if not Inter, assume it works (fonts are in Docker)
  let finalFamily = family;
  let finalWeight = weight;

  const familyName = /\s/.test(finalFamily) ? `'${finalFamily}'` : finalFamily;
  const fontSpec = `${finalWeight} ${fontPx}px ${familyName}`;
  ctx.font = fontSpec;



  ctx.textAlign = 'center';
  ctx.textBaseline = 'alphabetic';
  const y = height - Math.round(height * (Number(style.yOffsetPercent || 40) / 100));
  ctx.translate(Math.round(width/2), Math.round(y + (Number(opts?.translateY)||0)));
  const scl = Number.isFinite(opts?.scale) ? Number(opts.scale) : 1;
  if (scl !== 1) ctx.scale(scl, scl);

  // Render text with proper drop shadow like in the editor
  const shadowSize = Math.max(0, Number(style.shadowSize || 0));
  const shadowColor = style.shadowColor || '#000000';
  const textColor = style.color || '#ffffff';
  const opacity = Number.isFinite(opts?.opacity) ? Math.max(0, Math.min(1, Number(opts.opacity))) : 1;

  // Disable canvas built-in shadow to avoid glow effect
  ctx.shadowColor = 'transparent';
  ctx.shadowBlur = 0;
  ctx.shadowOffsetX = 0;
  ctx.shadowOffsetY = 0;

  // Render shadow first (behind text) - simpler approach that works
  if (shadowSize > 0) {
    ctx.save();
    ctx.fillStyle = shadowColor;

    // Create smooth shadow with multiple layers
    const layers = Math.max(3, Math.min(8, Math.round(shadowSize * 0.6)));
    const baseOffset = shadowSize * 0.25;
    const totalShadowAlpha = Math.min(0.6, shadowSize * 0.04);

    for (let i = 0; i < layers; i++) {
      const layerProgress = (i + 1) / layers;
      const layerOffset = baseOffset * layerProgress;
      const layerAlpha = (totalShadowAlpha / layers) * (1 - layerProgress * 0.3);

      ctx.save();
      ctx.globalAlpha = layerAlpha;
      ctx.translate(layerOffset, layerOffset * 1.2);
      ctx.fillText(String(text), 0, 0);
      ctx.restore();
    }

    ctx.restore();
  }

  // Then render main text on top
  ctx.save();
  ctx.globalAlpha = opacity;
  ctx.fillStyle = textColor;
  ctx.fillText(String(text), 0, 0);
  ctx.restore();

  ctx.restore();
}

function drawWatermark(ctx, width, height) {
  ctx.save();
  const wmText = 'TRIAL EXPORT — Upgrade for no watermark ($9/mo)';
  ctx.globalAlpha = 0.85;
  ctx.font = `800 ${Math.round(Math.max(18, width * 0.03))}px Inter, sans-serif`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'bottom';
  const metrics = ctx.measureText(wmText);
  const padX = 18, padY = 10;
  const textW = metrics.width;
  const boxW = textW + padX*2;
  const boxH = Math.round((width * 0.03) + padY*2);
  const x = Math.round(width/2);
  const y = height - Math.round(height * 0.06);
  ctx.fillStyle = 'rgba(0,0,0,0.35)';
  ctx.fillRect(x - boxW/2, y - boxH + 6, boxW, boxH);
  ctx.fillStyle = 'rgba(255,255,255,0.95)';
  ctx.fillText(wmText, x, y);
  ctx.restore();
}


