import express from 'express';
import fetch from 'node-fetch';
import { execa } from 'execa';
import fs from 'node:fs';
import fsp from 'node:fs/promises';
import path from 'node:path';
import os from 'node:os';
import { fileURLToPath } from 'node:url';
import { v4 as uuidv4 } from 'uuid';
import tmp from 'tmp';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { renderOverlayFrames } from './renderer.js';

const app = express();
app.use(express.json({ limit: '5mb' }));

// Simple bearer auth
function requireAuth(req, res, next) {
  const token = (req.headers['authorization'] || '').replace(/^Bearer\s+/i, '').trim();
  if (!token || token !== process.env.EXPORT_SERVICE_SECRET) {
    return res.status(401).json({ error: 'unauthorized' });
  }
  next();
}

// In-memory job store (replace with Redis/DB for durability)
const jobs = new Map();

// R2 S3 client
const s3 = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY
  }
});

// Detect NVENC availability once at startup
let nvencAvailable = false;
(async () => {
  try {
    const { stdout } = await execa('ffmpeg', ['-hide_banner', '-encoders']);
    nvencAvailable = /h264_nvenc/.test(stdout || '');
    // eslint-disable-next-line no-console
    console.log('NVENC available:', nvencAvailable);
  } catch (e) {
    // ignore
  }
})();

async function downloadToTemp(url) {
  const r = await fetch(url);
  if (!r.ok) throw new Error(`download_failed ${r.status}`);
  const tmpFile = tmp.fileSync({ postfix: path.extname(new URL(url).pathname) || '.mp4' });
  const ws = fs.createWriteStream(tmpFile.name);
  await new Promise((res, rej) => {
    r.body.pipe(ws);
    r.body.on('error', rej);
    ws.on('finish', res);
    ws.on('error', rej);
  });
  return tmpFile.name;
}

async function ffprobeJson(file) {
  const { stdout } = await execa('/usr/local/bin/ffprobe', ['-v', 'error', '-print_format', 'json', '-show_format', '-show_streams', file]);
  return JSON.parse(stdout);
}

async function uploadToR2(localPath, key, contentType) {
  await s3.send(new PutObjectCommand({
    Bucket: process.env.R2_BUCKET,
    Key: key,
    Body: fs.createReadStream(localPath),
    ContentType: contentType
  }));
  return key;
}

app.post('/jobs', requireAuth, async (req, res) => {
  try {
    const id = uuidv4();
    const payload = req.body || {};
    jobs.set(id, { id, status: 'queued', payload });

    // Fire-and-forget worker
    processJob(id).catch(err => {
      console.error('job failed', id, err);
      const j = jobs.get(id);
      if (j) { j.status = 'error'; j.error = String(err?.message || err); jobs.set(id, j); }
    });
    res.json({ jobId: id });
  } catch (e) {
    console.error(e);
    res.status(500).json({ error: 'create_failed' });
  }
});

app.get('/jobs/:id', requireAuth, (req, res) => {
  const j = jobs.get(req.params.id);
  if (!j) return res.status(404).json({ error: 'not_found' });
  res.json({ status: j.status, result: j.result, error: j.error });
});

// Debug endpoint to check font registration (no auth required for debugging)
app.get('/debug/fonts', (req, res) => {
  res.json({
    message: 'Check server logs for font registration details',
    note: 'Restart the server to see font scanning logs',
    timestamp: new Date().toISOString()
  });
});

// Debug: return ffmpeg encoders list (to verify NVENC)
app.get('/debug/encoders', requireAuth, async (_req, res) => {
  try {
    const { stdout } = await execa('ffmpeg', ['-hide_banner', '-encoders']);
    res.type('text/plain').send(stdout);
  } catch (e) {
    const msg = (e && typeof e === 'object' && 'message' in e) ? String(e.message) : String(e);
    res.status(500).json({ error: 'ffmpeg_failed', details: msg });
  }
});

async function processJob(id) {
  const j = jobs.get(id);
  if (!j) return;
  j.status = 'processing'; jobs.set(id, j);

  const { source, segments, captionStyle, videoFX, watermark, output, userId } = j.payload;
  const signedURL = source?.signedURL;
  if (!signedURL) throw new Error('no_source');

  // 1) Download
  const inputPath = await downloadToTemp(signedURL);

  // 2) Probe
  const meta = await ffprobeJson(inputPath);
  const vStream = (meta.streams || []).find(s => s.codec_type === 'video');
  const srcW = Number(vStream?.width || 1280);
  const srcH = Number(vStream?.height || 720);
  const fps = (() => {
    const r = (vStream?.r_frame_rate || '30/1').split('/');
    const f = Number(r[0]) / Number(r[1] || 1);
    return Math.max(15, Math.min(60, Math.round(f || 30)));
  })();
  const inputDuration = Number(meta.format?.duration || 0) || 0;
  const rotationDeg = getRotationDeg(vStream);
  const swap = Math.abs(rotationDeg) % 180 === 90;
  const width = swap ? srcH : srcW;
  const height = swap ? srcW : srcH;

  // 3) Prepare working dir and render alpha overlay via node-canvas
  const tmpDir = await fsp.mkdtemp(path.join(os.tmpdir(), 'cap-'));
  const framesDir = path.join(tmpDir, 'overlay_frames');
  await fsp.mkdir(framesDir, { recursive: true });
  await renderOverlayFrames({
    framesDir,
    width, height, fps,
    segments: Array.isArray(segments) ? segments : [],
    style: captionStyle || {},
    watermark: !!watermark,
    videoDuration: inputDuration
  });

  // 4) Build FFmpeg filters for base video FX
  const fx = videoFX || {};
  const brightnessAdj = (() => {
    const b = Number(fx.brightness ?? 1);
    const adj = b - 1; // FFmpeg eq expects additive [-1,1], our UI sends ~1.0 +/-
    return Math.max(-0.5, Math.min(0.5, isFinite(adj) ? adj : 0));
  })();
  const eq = `eq=saturation=${Number(fx.saturate||1)}:contrast=${Number(fx.contrast||1)}:brightness=${brightnessAdj}`;
  const hueDeg = Number(fx.hue || 0);
  const hue = (hueDeg !== 0) ? `hue=H=${(hueDeg * Math.PI / 180).toFixed(6)}` : null;
  const sepia = Number(fx.sepia||0) > 0 ? 'colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131' : null;
  const gray = Number(fx.grayscale||0) > 0 ? 'format=gray' : null;
  const invert = Number(fx.invert||0) > 0 ? 'lutrgb=r=negval:g=negval:b=negval' : null;
  const blur = Number(fx.blur||0) > 0 ? `gblur=sigma=${Math.max(0.1, Number(fx.blur))}` : null;
  const baseFilters = [eq, hue, sepia, gray, invert, blur].filter(Boolean).join(',');

  // 5) Encode overlay to alpha WebM and composite over base FX
  const outPath = path.join(tmpDir, 'out.mp4');
  const crf = String(output?.crf ?? 20);
  const preset = String(output?.preset ?? 'veryfast');
  const srcVideoBitrate = Number(vStream?.bit_rate || 0);
  const targetBitrate = srcVideoBitrate && isFinite(srcVideoBitrate) && srcVideoBitrate > 0 ? Math.round(srcVideoBitrate) : 0;
  // Directly overlay PNG sequence with alpha (no intermediate codec dependency)
  const fullVf = `[0:v]${baseFilters}[vf];[1:v]format=rgba,setsar=1[ov];[vf][ov]overlay=shortest=1:format=auto[vout]`;
  const useNvenc = nvencAvailable && !process.env.FORCE_CPU;
  const ffArgs = [
    '-nostdin', '-y', '-i', inputPath,
    '-framerate', String(fps), '-thread_queue_size', '512', '-i', path.join(framesDir, '%06d.png'),
    '-filter_complex', fullVf,
    '-map', '[vout]', '-map', '0:a:0?',
    '-c:v', useNvenc ? 'h264_nvenc' : 'libx264',
    ...(useNvenc ? ['-preset', 'p5', '-rc', 'vbr'] : ['-preset', preset]),
    '-r', String(fps), '-g', String(Math.max(1, fps*2)), '-pix_fmt', 'yuv420p',
    ...(useNvenc ? [] : ['-threads', '0']),
    '-c:a', 'aac', '-b:a', String(output?.audioBitrate || 160000), '-movflags', '+faststart',
    outPath
  ];
  if (targetBitrate > 0) {
    // Use ABR to roughly match source
    const insertAt = ffArgs.indexOf('-r');
    ffArgs.splice(insertAt, 0, ...(useNvenc ? ['-cq', '23'] : []), '-b:v', String(targetBitrate), '-maxrate', String(Math.round(targetBitrate*1.5)), '-bufsize', String(Math.round(targetBitrate*3)));
  } else {
    // Fall back to CRF/CQ
    const insertAt = ffArgs.indexOf('-r');
    ffArgs.splice(insertAt, 0, ...(useNvenc ? ['-cq', crf] : ['-crf', crf]));
  }
  try {
    await execa('ffmpeg', ffArgs);
  } catch (e) {
    // If NVENC failed (driver/API mismatch), retry on CPU
    if (useNvenc) {
      const cpuArgs = [
        '-nostdin', '-y', '-i', inputPath,
        '-filter_complex', fullVf,
        '-map', '[vout]', '-map', '0:a:0?',
        '-c:v', 'libx264', '-preset', preset, '-r', String(fps), '-g', String(Math.max(1, fps*2)), '-pix_fmt', 'yuv420p', '-threads', '0',
        '-c:a', 'aac', '-b:a', String(output?.audioBitrate || 160000), '-movflags', '+faststart',
        outPath
      ];
      if (targetBitrate > 0) {
        const ins = cpuArgs.indexOf('-r');
        cpuArgs.splice(ins, 0, '-b:v', String(targetBitrate), '-maxrate', String(Math.round(targetBitrate*1.5)), '-bufsize', String(Math.round(targetBitrate*3)));
      } else {
        const ins = cpuArgs.indexOf('-r');
        cpuArgs.splice(ins, 0, '-crf', crf);
      }
      await execa('ffmpeg', cpuArgs);
    } else {
      throw e;
    }
  }

  // 7) Upload to R2
  const outKey = `exports/${(j.payload.userId||'user')}/${id}.mp4`;
  await uploadToR2(outPath, outKey, 'video/mp4');

  j.status = 'completed';
  j.result = { r2Key: outKey };
  jobs.set(id, j);

  // Cleanup best-effort
  try { await fsp.rm(tmpDir, { recursive: true, force: true }); } catch {}
}

const port = process.env.PORT || 8080;
app.listen(port, () => console.log(`Exporter listening on ${port}`));

function toAssFromSegments(segments, opts){
  const {
    width, height,
    fontFamily, fontSize, yOffsetPercent,
    colorHex, outlineSize, uppercase, emphasizeColorHex,
    addWatermark, duration,
    wordEffect, effectOnlyEmphasized, effectDurationMs, emphasizeScale
  } = opts || {};
  const playResX = Math.max(320, Number(width||1280));
  const playResY = Math.max(240, Number(height||720));

  // Apply font scaling based on video resolution (same as canvas export)
  const baseResolution = 720;
  const scaleFactor = Math.max(playResX, playResY) / baseResolution;
  const scaledFontSize = Math.round(Number(fontSize || 36) * scaleFactor);

  // Use the first font from the CSS font stack directly (like canvas export)
  const fontStack = String(fontFamily || 'Inter').split(',').map(f => f.trim().replace(/^['"]|['"]$/g,''));
  let selectedFont = fontStack[0] || 'Inter'; // Use the first font directly

  // Only map system generics to specific fonts
  if (selectedFont === 'system-ui' || selectedFont === '-apple-system' || selectedFont === 'Segoe UI') {
    selectedFont = 'Inter';
  }
  // For everything else (Poppins, Montserrat, DM Sans, Lato, etc.), use as-is

  const primary = toAssColor(colorHex || '#FFFFFF');
  const outline = toAssColor('#000000');
  const emph = toAssColor(emphasizeColorHex || '#f59e0b');
  const alignBottomCenter = 2; // ASS alignment code 2 = bottom center
  const marginV = Math.round(playResY * (Number(yOffsetPercent||10)/100));

  let ass = `[Script Info]\nScriptType: v4.00+\nPlayResX: ${playResX}\nPlayResY: ${playResY}\nScaledBorderAndShadow: yes\n\n`+
  `[V4+ Styles]\n`+
  `Format: Name,Fontname,Fontsize,PrimaryColour,SecondaryColour,OutlineColour,BackColour,Bold,Italic,Underline,StrikeOut,`+
  `ScaleX,ScaleY,Spacing,Angle,BorderStyle,Outline,Shadow,Alignment,MarginL,MarginR,MarginV,Encoding\n`+
  `Style: Cap,${selectedFont},${scaledFontSize},${primary},&H000000FF,${outline},&H00000000,-1,0,0,0,100,100,0,0,1,${Math.max(1, Number(outlineSize||4))},0,${alignBottomCenter},60,60,${marginV},0\n\n`+
  `[Events]\nFormat: Layer,Start,End,Style,Name,MarginL,MarginR,MarginV,Effect,Text\n`;

  function ts(t){
    const h = Math.floor(t/3600); const m = Math.floor((t%3600)/60); const s = Math.floor(t%60); const cs = Math.floor((t%1)*100);
    return `${String(h).padStart(1,'0')}:${String(m).padStart(2,'0')}:${String(s).padStart(2,'0')}.${String(cs).padStart(2,'0')}`;
  }

  for (const seg of segments) {
    const text = String(seg.text||'').trim();
    if (!text) continue;
    const tokens = text.split(/\s+/);
    const segDur = Math.max(0.001, (seg.end ?? seg.start) - seg.start);
    let durs = (Array.isArray(seg.wordDurations) && seg.wordDurations.length === tokens.length)
      ? seg.wordDurations.slice() : tokens.map(()=> segDur/Math.max(1,tokens.length));
    const sum = durs.reduce((a,b)=>a+b,0) || 1; const scaleNorm = segDur / sum; durs = durs.map(d=>d*scaleNorm);

    const accel = 2.0; // approximate ease-out
    const effectMs = Math.max(50, Math.round(Number(opts.effectDurationMs || 250)));
    const effect = String(opts.wordEffect || 'pop');
    const onlyEmph = !!opts.effectOnlyEmphasized;

    let tCursor = 0;
    const parts = tokens.map((tok, i) => {
      const word = uppercase ? String(tok).toUpperCase() : String(tok);
      const dur = Math.max(0, durs[i] || 0);
      const kCenti = Math.max(1, Math.round(dur * 100));
      const startMs = Math.round(tCursor * 1000);
      const endMs = startMs + effectMs;
      const emphasized = Boolean(seg.emphasizeSegment) || Boolean((seg.emphasizedWordIndices||[]).includes(i));
      const apply = !onlyEmph || emphasized;
      let scaleStart = 100;
      if (apply) {
        scaleStart = effect === 'scale' ? Math.round((0.6) * 100) : Math.round((0.85) * 100);
      }
      const scaleTarget = emphasized ? Math.round(Number(opts.emphasizeScale || 1.35) * 100) : 100;
      const colorTag = emphasized ? `\\c${toAssRgb(emphasizeColorHex)}` : '';
      const pre = apply ? `\\fscx${scaleStart}\\fscy${scaleStart}\\t(${startMs},${endMs},${accel},\\fscx${scaleTarget}\\fscy${scaleTarget})` : '';
      tCursor += dur;
      return `{\\k${kCenti}${pre}${colorTag}}${word}`;
    }).join(' ');

    ass += `Dialogue: 0,${ts(seg.start)},${ts(seg.end)},Cap,,0,0,0,,${parts}\n`;
  }

  if (addWatermark && duration > 0) {
    const start = 0; const end = duration;
    const wmText = 'TRIAL EXPORT — Upgrade for no watermark ($9/mo)';
    const wm = uppercase ? wmText.toUpperCase() : wmText;
    // Bottom center, small margin. Use same style.
    ass += `Dialogue: 5,${ts(start)},${ts(end)},Cap,,0,0,0,,${wm}\n`;
  }

  return ass;
}

function toAssColor(hex){
  // ASS color &HAABBGGRR (BGR order), AA is alpha
  const m = String(hex||'').trim().match(/^#?([0-9a-f]{6}|[0-9a-f]{3})$/i);
  let r=255,g=255,b=255;
  if (m) {
    let h = m[1];
    if (h.length === 3) h = h.split('').map(c=>c+c).join('');
    r = parseInt(h.slice(0,2),16); g = parseInt(h.slice(2,4),16); b = parseInt(h.slice(4,6),16);
  }
  const to2 = (n)=> n.toString(16).toUpperCase().padStart(2,'0');
  // No alpha in primary (00)
  return `&H00${to2(b)}${to2(g)}${to2(r)}`;
}

function toAssRgb(hex){
  const m = String(hex||'').trim().match(/^#?([0-9a-f]{6}|[0-9a-f]{3})$/i);
  let r=255,g=255,b=255;
  if (m) {
    let h = m[1];
    if (h.length === 3) h = h.split('').map(c=>c+c).join('');
    r = parseInt(h.slice(0,2),16); g = parseInt(h.slice(2,4),16); b = parseInt(h.slice(4,6),16);
  }
  const to2 = (n)=> n.toString(16).toUpperCase().padStart(2,'0');
  // \c expects &HBBGGRR
  return `&H00${to2(b)}${to2(g)}${to2(r)}`;
}

function getRotationDeg(vStream){
  try {
    const side = vStream?.side_data_list || vStream?.side_data || [];
    for (const s of side) {
      if (!s) continue;
      if (typeof s.rotation === 'number') return Number(s.rotation);
      if (typeof s.displaymatrix === 'string' && /rotation of ([\-\d.]+)/i.test(s.displaymatrix)) {
        const m = s.displaymatrix.match(/rotation of ([\-\d.]+)/i);
        return m ? Number(m[1]) : 0;
      }
    }
  } catch {}
  return 0;
}


