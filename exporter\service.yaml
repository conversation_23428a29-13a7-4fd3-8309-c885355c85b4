Remove-Item -Force .\service.yaml -ErrorAction SilentlyContinue

Set-Content -Encoding UTF8 -Path .\service.yaml -Value @'
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: capsy-exporter
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: 'false'
        run.googleapis.com/gpu-zonal-redundancy-disabled: 'true'
    spec:
      containerConcurrency: 1
      containers:
      - image: europe-west1-docker.pkg.dev/capsye-renderer/cloud-run-source-deploy/capsy-exporter:gpu
        resources:
          limits:
            cpu: '4'
            memory: '16Gi'
            nvidia.com/gpu: '1'
      nodeSelector:
        run.googleapis.com/accelerator: nvidia-l4
'@