function getCookie(name: string, cookie: string | null) {
  const m = cookie?.match(new RegExp(`(?:^|; )${name}=([^;]+)`));
  return m ? decodeURIComponent(m[1]) : null;
}

export async function onRequestPost(ctx: any) {
  const { request, env } = ctx;

  // Require authenticated session
  const sid = getCookie("sid", request.headers.get("cookie"));
  if (!sid) return new Response(JSON.stringify({ error: "unauthorized" }), { status: 401 });
  const raw = await env.SESSIONS.get(`sid:${sid}`);
  if (!raw) return new Response(JSON.stringify({ error: "unauthorized" }), { status: 401 });
  const sess = JSON.parse(raw);
  const userId = String(sess.userId || sess.id || "unknown");

  const ctype = request.headers.get("content-type") || "";
  if (!ctype.includes("multipart/form-data")) {
    return new Response(
      JSON.stringify({ error: "multipart/form-data required with file field" }),
      { status: 415 }
    );
  }

  const form = await request.formData();
  const file = form.get("file") as File | null;
  if (!file) return new Response(JSON.stringify({ error: "file missing" }), { status: 400 });

  const bucket = (env as any).bucket || (env as any).R2 || (env as any).BUCKET;
  if (!bucket) {
    return new Response(JSON.stringify({ error: "r2_not_configured" }), { status: 500 });
  }

  // Configurable limits with sensible defaults
  const maxUploadMb = Number(env.MAX_UPLOAD_MB || 200); // per-file size cap for free tier
  const maxDailyUploads = Number(env.MAX_DAILY_UPLOADS || 3); // per user per day
  const maxDailyMb = Number(env.MAX_DAILY_MB || 500); // total bytes per day per user

  // Validate file type and size early
  const ct = (file.type || "application/octet-stream").toLowerCase();
  const allowed = ct.startsWith("video/") || ct.startsWith("audio/");
  if (!allowed) {
    return new Response(JSON.stringify({ error: "unsupported_type", allowed: ["video/*", "audio/*"] }), { status: 415 });
  }
  if (Number.isFinite(maxUploadMb) && file.size > maxUploadMb * 1024 * 1024) {
    return new Response(JSON.stringify({ error: "file_too_large", maxMb: maxUploadMb }), { status: 413 });
  }

  // Per-user daily quotas using KV (approximate, good enough for small scale)
  const today = new Date();
  const y = today.getUTCFullYear();
  const m = String(today.getUTCMonth() + 1).padStart(2, "0");
  const d = String(today.getUTCDate()).padStart(2, "0");
  const dayKeyPrefix = `u:${userId}:d:${y}-${m}-${d}:upload:`;
  const list = await env.SESSIONS.list({ prefix: dayKeyPrefix });
  const uploadsToday = (list?.keys || []).length;
  if (uploadsToday >= maxDailyUploads) {
    return new Response(JSON.stringify({ error: "daily_upload_limit_reached", limit: maxDailyUploads }), { status: 429 });
  }
  let bytesToday = 0;
  for (const k of list.keys || []) {
    const v = await env.SESSIONS.get(k.name);
    const n = v ? Number(v) : 0;
    if (!Number.isNaN(n)) bytesToday += n;
    if (bytesToday > maxDailyMb * 1024 * 1024) break;
  }
  if (Number.isFinite(maxDailyMb) && (bytesToday + file.size) > maxDailyMb * 1024 * 1024) {
    return new Response(
      JSON.stringify({ error: "daily_bytes_limit_reached", limitMb: maxDailyMb, usedMb: Math.ceil(bytesToday/1024/1024) }),
      { status: 429 }
    );
  }

  const origName = (file.name || "upload.bin").replace(/[^\w.\-]+/g, "_").slice(0, 120);
  // Store under temporary, per-user prefix to enable lifecycle cleanup and per-user browsing if needed
  const key = `uploads/tmp/${userId}/${crypto.randomUUID()}-${origName}`;

  // Stream to R2 to avoid loading the entire file into memory
  const put = await bucket.put(key, file.stream(), {
    httpMetadata: {
      contentType: file.type || "application/octet-stream",
    },
  });

  // Track this upload in KV with TTL that expires at next UTC midnight
  const ttlSeconds = Math.max(60, Math.floor((Date.UTC(y, Number(m)-1, Number(d)+1) - Date.now()) / 1000));
  const recordKey = `${dayKeyPrefix}${crypto.randomUUID()}`;
  await env.SESSIONS.put(recordKey, String(put?.size || file.size), { expirationTtl: ttlSeconds });

  // Create short-lived download token mapped to this key (valid 30 min)
  const token = crypto.randomUUID();
  await env.SESSIONS.put(`dl:${token}`, key, { expirationTtl: 60 * 30 });

  const origin = new URL(request.url);
  const fileURL = `${origin.origin}/api/file?key=${encodeURIComponent(key)}&token=${encodeURIComponent(token)}`;

  return new Response(
    JSON.stringify({ key, etag: put?.etag || null, size: put?.size || null, uploaded: true, fileURL }),
    { headers: { "content-type": "application/json" } }
  );
}


