function getCookie(name: string, cookie: string | null) {
  const m = cookie?.match(new RegExp(`(?:^|; )${name}=([^;]+)`));
  return m ? decodeURIComponent(m[1]) : null;
}

export async function onRequestGet(ctx: any) {
  const { request, env } = ctx;
  const sid = getCookie("sid", request.headers.get("cookie"));
  if (!sid)
    return new Response(JSON.stringify({ user: null }), {
      headers: { "content-type": "application/json" },
    });

  const raw = await env.SESSIONS.get(`sid:${sid}`);
  if (!raw)
    return new Response(JSON.stringify({ user: null }), {
      headers: { "content-type": "application/json" },
    });

  const sess = JSON.parse(raw);
  return new Response(JSON.stringify({ user: { id: sess.userId, email: sess.email, provider: sess.provider || 'email' } }), {
    headers: { "content-type": "application/json" },
  });
}


