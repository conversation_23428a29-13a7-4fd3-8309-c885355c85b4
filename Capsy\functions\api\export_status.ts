export async function onRequestGet(ctx: any) {
  const { request, env } = ctx;

  // Auth: require valid session
  const cookie = request.headers.get('cookie') || '';
  const m = cookie.match(/(?:^|; )sid=([^;]+)/);
  const sid = m ? decodeURIComponent(m[1]) : null;
  if (!sid) return new Response(JSON.stringify({ error: 'unauthorized' }), { status: 401 });
  const sessRaw = await env.SESSIONS.get(`sid:${sid}`);
  if (!sessRaw) return new Response(JSON.stringify({ error: 'unauthorized' }), { status: 401 });

  const url = new URL(request.url);
  const jobId = url.searchParams.get('jobId');
  if (!jobId) return new Response(JSON.stringify({ error: 'jobId_required' }), { status: 400 });

  const exporterURL = (env.EXPORT_SERVICE_URL || '').toString().trim();
  const exporterSecret = (env.EXPORT_SERVICE_SECRET || '').toString().trim();
  if (!exporterURL || !exporterSecret) {
    return new Response(JSON.stringify({ error: 'export_service_not_configured' }), { status: 500 });
  }

  const r = await fetch(`${exporterURL.replace(/\/$/, '')}/jobs/${encodeURIComponent(jobId)}`, {
    headers: { 'authorization': `Bearer ${exporterSecret}` }
  });
  if (!r.ok) {
    const txt = await r.text().catch(()=> '');
    return new Response(JSON.stringify({ error: 'exporter_error', details: txt.slice(0,500) }), { status: 502 });
  }
  const data = await r.json().catch(()=>null);

  // If the exporter returns an r2Key for download, mint a tokenized URL
  if (data?.status === 'completed' && data?.result?.r2Key) {
    const r2Key = String(data.result.r2Key);
    const token = crypto.randomUUID();
    await env.SESSIONS.put(`dl:${token}`, r2Key, { expirationTtl: 60 * 30 });
    const origin = new URL(request.url).origin;
    const downloadURL = `${origin}/api/file?key=${encodeURIComponent(r2Key)}&token=${encodeURIComponent(token)}`;
    return new Response(JSON.stringify({ status: 'completed', downloadURL }), { headers: { 'content-type': 'application/json' } });
  }

  return new Response(JSON.stringify(data || { status: 'unknown' }), { headers: { 'content-type': 'application/json' } });
}


