:root {
  --capsy-blue: #222222; /* dark blue */
  --capsy-orange: #1DCD9F; /* accent */
  --capsy-yellow: #169976; /* accent */
  --capsy-light: #EAEFEF; /* light blue */
}
/* Backdrop */
body {
  background: radial-gradient(1200px 600px at 20% -10%, #1dcd9eb6, transparent),
              radial-gradient(1000px 500px at 120% 10%, #16997634, transparent),
              var(--capsy-blue);
  color: var(--capsy-light);
}
/* Themed containers and borders */
.theme-card { background: rgba(235, 238, 255, 0.06) !important; }
.theme-border { border-color: rgba(235, 238, 255, 0.16) !important; }
.theme-muted { color: rgba(235, 238, 255, 0.72) !important; }
.theme-strong { color: var(--capsy-light) !important; }
.theme-accent { color: var(--capsy-yellow) !important; }
.btn-accent { background: var(--capsy-orange) !important; color: #0b0b0b !important; }
.btn-accent:hover { filter: brightness(1.1); }
.btn-outline { background: #eaefef11 !important; border: 1px solid #eaefef1f !important; }
/* Header gradient using brand blue */
.site-header { background-image: linear-gradient(to bottom, #22222221, #0000002c) !important; }
/* Subtitles overlay */
.subtitle-overlay {
  position:absolute; left:0; right:0; bottom:6%;
  text-align:center; padding:0.35rem 0.75rem;
  font-size: clamp(14px, 2.2vw, 22px);
  pointer-events:none;
}
.word-pop {
  display:inline-block;
  transform-origin:center;
  animation: wordPop 220ms ease-out;
}
@keyframes wordPop { from { transform: scale(0.8); opacity: 0.2; } to { transform: scale(1); opacity: 1; } }
.seg-thumb {
  height: 22px; min-width: 6px; border-radius: 6px;
  display:inline-block; margin-right:4px;
  background: linear-gradient(180deg, #EAEFEF, #B8CFCE);
  box-shadow: inset 0 0 0 1px rgba(255,255,255,.06);
}
.seg-thumb.active { outline: 2px solid #1dcd9ee3; }
.seg-thumb-added {
  height: 22px; min-width: 6px; border-radius: 6px;
  display:inline-block; margin-right:4px;
  background: linear-gradient(180deg, rgba(235,238,255,.30), rgba(235,238,255,.18));
  box-shadow: inset 0 0 0 1px rgba(255,255,255,.06);
}
.seg-thumb-added.active { outline: 2px solid rgba(235,238,255,.85); }
.word-thumb {
  height: 12px; min-width: 3px; border-radius: 4px;
  display:inline-block; margin-right:2px;
  background: linear-gradient(180deg, #EAEFEF, #B8CFCE);
  box-shadow: inset 0 0 0 1px rgba(255,255,255,.06);
}
.word-thumb.active { outline: 1px solid #1dcd9ee8; }
.hide-scrollbar::-webkit-scrollbar { height:8px; width:8px; }
.hide-scrollbar::-webkit-scrollbar-thumb { background:#374151; border-radius:8px; }
.input-num { width: 9ch; }
.grid-min { grid-template-columns: 100px 100px 1fr 90px; }

/* Brand overrides: map indigo/emerald/rose utilities to Capsy brand */
.bg-indigo-600 { background-color: var(--capsy-orange) !important; }
.hover\:bg-indigo-500:hover { background-color: #B8CFCE !important; }
.text-indigo-300 { color: #EAEFEF !important; }
.border-indigo-500\/20 { border-color: #169976 !important; }
.border-indigo-400\/30 { border-color: #169976 !important; }
.bg-indigo-500\/10 { background-color: #169976 !important; }
.bg-indigo-600\/20 { background-color: #169976 !important; }
.shadow-indigo-600\/25 { box-shadow: 0 10px 15px -3px #169976, 0 4px 6px -4px #169976 !important; }

.bg-emerald-600 { background-color: var(--capsy-orange) !important; }
.hover\:bg-emerald-500:hover { background-color: #B8CFCE !important; }
.text-emerald-400 { color: var(--capsy-yellow) !important; }

.bg-rose-600 { background-color: var(--capsy-orange) !important; }
.hover\:bg-rose-500:hover { background-color: #B8CFCE !important; }

/* Form controls accent color */
.accent-indigo-500 { accent-color: var(--capsy-orange) !important; }


