function bad(status: number, msg: string) {
  return new Response(JSON.stringify({ error: msg }), { status, headers: { 'content-type': 'application/json' } });
}

export async function onRequestGet(ctx: any) {
  const { request, env } = ctx;
  const url = new URL(request.url);
  const key = url.searchParams.get('key');
  if (!key) return bad(400, 'missing_params');

  const bucket = (env as any).bucket || (env as any).R2 || (env as any).BUCKET;
  if (!bucket) return bad(500, 'r2_not_configured');

  const obj = await bucket.get(key);
  if (!obj) return bad(404, 'not_found');

  const headers = new Headers();
  const ct = obj.httpMetadata?.contentType || 'application/octet-stream';
  headers.set('content-type', ct);
  // Shorter cache to allow quicker logo updates. Adjust if needed.
  headers.set('cache-control', 'public, max-age=60, must-revalidate');

  return new Response(obj.body, { headers });
}


