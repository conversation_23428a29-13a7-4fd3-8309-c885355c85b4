export async function onRequestGet(ctx: any) {
  const { request, env } = ctx;
  const url = new URL(request.url);
  const code = url.searchParams.get('code');
  const state = url.searchParams.get('state');
  if (!code || !state) return new Response('Invalid request', { status: 400 });

  const clientId = env.GOOGLE_CLIENT_ID as string | undefined;
  const clientSecret = env.GOOGLE_CLIENT_SECRET as string | undefined;
  if (!clientId || !clientSecret) {
    return new Response('Google OAuth not configured', { status: 500 });
  }

  const stateDataRaw = await env.SESSIONS.get(`oauth:state:${state}`);
  if (!stateDataRaw) return new Response('Session expired', { status: 400 });
  const { codeVerifier, redirectUri } = JSON.parse(stateDataRaw || '{}');
  if (!codeVerifier || !redirectUri) return new Response('Session expired', { status: 400 });

  await env.SESSIONS.delete(`oauth:state:${state}`);

  // Exchange code for tokens
  const tokenResp = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      client_id: clientId,
      client_secret: clientSecret,
      code,
      code_verifier: codeVerifier,
      redirect_uri: redirectUri,
      grant_type: 'authorization_code',
    }),
  });
  if (!tokenResp.ok) {
    const t = await tokenResp.text().catch(()=> '');
    return new Response(`OAuth token error: ${t}`, { status: 400 });
  }
  const tokenJson: any = await tokenResp.json();
  const idToken = tokenJson.id_token;
  const accessToken = tokenJson.access_token;
  if (!accessToken) return new Response('OAuth token missing', { status: 400 });

  // Fetch user profile (email)
  const userResp = await fetch('https://openidconnect.googleapis.com/v1/userinfo', {
    headers: { Authorization: `Bearer ${accessToken}` },
  });
  if (!userResp.ok) return new Response('Failed to fetch profile', { status: 400 });
  const profile: any = await userResp.json();
  const email = String(profile.email || '').toLowerCase().trim();
  if (!email) return new Response('No email from Google', { status: 400 });

  const db = env.DB as D1Database;
  let user = await db
    .prepare('SELECT id, email, provider FROM users WHERE email = ?')
    .bind(email)
    .first<{ id: number; email: string; provider: string }>();
  if (!user) {
    // Insert user; support deployments without the optional 'verified' column
    try {
      await db.prepare('INSERT INTO users (email, provider, verified) VALUES (?, ?, 1)')
        .bind(email, 'google').run();
    } catch (_) {
      await db.prepare('INSERT INTO users (email, provider) VALUES (?, ?)')
        .bind(email, 'google').run();
    }
    user = await db.prepare('SELECT id, email, provider FROM users WHERE email = ?').bind(email).first();
  } else {
    // Mark verified if column exists (ignore errors)
    try { await db.prepare('UPDATE users SET verified = 1 WHERE id = ?').bind((user as any).id).run(); } catch(_) {}
    try { await db.prepare('UPDATE users SET provider = ? WHERE id = ?').bind('google', (user as any).id).run(); } catch(_) {}
  }

  // Create session
  const sid = crypto.randomUUID();
  const ttl = 60 * 60 * 24 * 30;
  const session = { userId: (user as any).id, email: (user as any).email, provider: 'google' };
  await env.SESSIONS.put(`sid:${sid}`, JSON.stringify(session), { expirationTtl: ttl });
  const cookie = [`sid=${sid}`, 'Path=/', 'HttpOnly', 'Secure', 'SameSite=Lax', `Max-Age=${ttl}`].join('; ');

  return new Response(null, { status: 302, headers: { Location: '/#/editor', 'set-cookie': cookie } });
}


