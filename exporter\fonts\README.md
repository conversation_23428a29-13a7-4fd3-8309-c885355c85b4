Fonts for exporter (node-canvas)

Place .ttf files for the fonts you use in the editor here so the exporter can embed them.

How it works
- The exporter tries to register fonts on startup from these locations:
  - exporter/fonts (this folder)
  - /usr/share/fonts/truetype/custom, /usr/share/fonts/truetype, /usr/local/share/fonts (if present)
- Families recognized out of the box: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Archivo Black, Inter
- Inter is pre-registered; others must be added here as .ttf files.

Where to get fonts
- Google Fonts: https://fonts.google.com/ (Download family → unzip → use the static TTF for the weight you want)
- Windows: C:\\Windows\\Fonts (copy the .ttf file)

Recommended files per family (match the editor’s default weight ~800 unless noted):
- Poppins → Poppins-ExtraBold.ttf (800) or Poppins-Black.ttf (900)
- Montserrat → Montserrat-ExtraBold.ttf (800) or Montserrat-Black.ttf (900)
- <PERSON> → <PERSON>-Bold.ttf (700)
- <PERSON><PERSON> → BebasNeue-Regular.ttf (400)
- <PERSON> → Anton-Regular.ttf (400/700)
- <PERSON><PERSON> → Lexend-ExtraBold.ttf (800)
- Outfit → Outfit-ExtraBold.ttf (800)
- Barlow → Barlow-ExtraBold.ttf (800)
- Archivo Black → ArchivoBlack-Regular.ttf (400)

Folder structure (expected by the auto-registrar):
- exporter/fonts/poppins/Poppins-ExtraBold.ttf
- exporter/fonts/montserrat/Montserrat-ExtraBold.ttf
- exporter/fonts/oswald/Oswald-Bold.ttf
- exporter/fonts/bebasneue/BebasNeue-Regular.ttf
- exporter/fonts/anton/Anton-Regular.ttf
- exporter/fonts/lexend/Lexend-ExtraBold.ttf
- exporter/fonts/outfit/Outfit-ExtraBold.ttf
- exporter/fonts/barlow/Barlow-ExtraBold.ttf
- exporter/fonts/archivoblack/ArchivoBlack-Regular.ttf

Notes
- Only static TTFs are supported by node-canvas. Variable fonts may not render correctly.
- If the exporter logs still show Inter being used, the TTF wasn’t found. Double-check the path and file name.
- You can also use a root-level fonts folder (./fonts) with the same subfolder names; the exporter searches that too.

