﻿# -------- Stage 1: build FFmpeg (NVENC 12.1) --------
FROM ubuntu:22.04 AS build
ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y --no-install-recommends \
  build-essential pkg-config git curl ca-certificates \
  yasm nasm cmake python3 \
  libass-dev libfreetype6-dev libfribidi-dev libharfbuzz-dev \
  libfontconfig1-dev && rm -rf /var/lib/apt/lists/*

# nv-codec-headers pinned to 12.1 (matches Cloud Run L4 driver)
RUN git clone https://github.com/FFmpeg/nv-codec-headers.git /tmp/nv && \
    cd /tmp/nv && git checkout n12.1.14.0 && make && make install

# Build FFmpeg 7.0.2 with NVENC + libass + fontconfig stack
ARG FFMPEG_VERSION=7.0.2
RUN curl -fsSL https://ffmpeg.org/releases/ffmpeg-${FFMPEG_VERSION}.tar.bz2 -o /tmp/ffmpeg.tar.bz2 && \
    mkdir -p /tmp/ffmpeg && tar -xjf /tmp/ffmpeg.tar.bz2 -C /tmp/ffmpeg --strip-components=1 && \
    cd /tmp/ffmpeg && \
    ./configure --prefix=/opt/ffmpeg \
      --disable-debug --disable-doc --disable-ffplay \
      --enable-gpl --enable-version3 \
      --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-fontconfig \
      --enable-nonfree --enable-nvenc --enable-pthreads && \
    make -j"$(nproc)" && make install

# -------- Stage 2: runtime --------
FROM ubuntu:22.04
ENV DEBIAN_FRONTEND=noninteractive
# Node.js 20 + ffmpeg runtime deps + fonts
RUN apt-get update && apt-get install -y --no-install-recommends \
  curl ca-certificates gnupg fontconfig fonts-dejavu-core fonts-dejavu-extra libass9 libfreetype6 libfribidi0 libharfbuzz0b \
  libfontconfig1 libxcb1 libxcb-shm0 libxcb-shape0 libxcb-xfixes0 libx11-6 libxext6 libdrm2 \
  libsdl2-2.0-0 libasound2 && \
  curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
  apt-get install -y --no-install-recommends nodejs && \
  rm -rf /var/lib/apt/lists/*

# Inter font
RUN mkdir -p /usr/share/fonts/truetype/inter && \
    apt-get update && apt-get install -y --no-install-recommends wget unzip && \
    wget -q https://github.com/rsms/inter/releases/download/v4.1/Inter-4.1.zip -O /tmp/inter.zip && \
    unzip -q /tmp/inter.zip -d /tmp/inter && \
    # Copy specific TTF files to the expected location
    find /tmp/inter -name "*.ttf" -exec cp {} /usr/share/fonts/truetype/inter/ \; && \
    ls -la /usr/share/fonts/truetype/inter/ && \
    rm -rf /tmp/inter.zip /tmp/inter && fc-cache -f -v && \
    # Common caption fonts used in the editor (install locally for node-canvas)
    mkdir -p /usr/share/fonts/truetype/custom && \
    # Download a heavy weight for each family so weight 800 looks right; fall back silently if a file is missing
    wget -q -O /usr/share/fonts/truetype/custom/Poppins-ExtraBold.ttf "https://github.com/google/fonts/raw/main/ofl/poppins/Poppins-ExtraBold.ttf" || true && \
    wget -q -O /usr/share/fonts/truetype/custom/Montserrat-ExtraBold.ttf "https://github.com/google/fonts/raw/main/ofl/montserrat/Montserrat-ExtraBold.ttf" || true && \
    wget -q -O /usr/share/fonts/truetype/custom/Oswald-Bold.ttf "https://github.com/google/fonts/raw/main/ofl/oswald/Oswald-Bold.ttf" || true && \
    wget -q -O /usr/share/fonts/truetype/custom/BebasNeue-Regular.ttf "https://github.com/google/fonts/raw/main/ofl/bebasneue/BebasNeue-Regular.ttf" || true && \
    wget -q -O /usr/share/fonts/truetype/custom/Anton-Regular.ttf "https://github.com/google/fonts/raw/main/ofl/anton/Anton-Regular.ttf" || true && \
    wget -q -O /usr/share/fonts/truetype/custom/Lexend-ExtraBold.ttf "https://github.com/google/fonts/raw/main/ofl/lexend/Lexend-ExtraBold.ttf" || true && \
    wget -q -O /usr/share/fonts/truetype/custom/Outfit-ExtraBold.ttf "https://github.com/google/fonts/raw/main/ofl/outfit/Outfit-ExtraBold.ttf" || true && \
    wget -q -O /usr/share/fonts/truetype/custom/Barlow-ExtraBold.ttf "https://github.com/google/fonts/raw/main/ofl/barlow/Barlow-ExtraBold.ttf" || true && \
    wget -q -O /usr/share/fonts/truetype/custom/ArchivoBlack-Regular.ttf "https://github.com/google/fonts/raw/main/ofl/archivoblack/ArchivoBlack-Regular.ttf" || true && \
    fc-cache -f -v && \
    apt-get purge -y unzip wget && apt-get autoremove -y && rm -rf /var/lib/apt/lists/*

# Install FFmpeg from builder
COPY --from=build /opt/ffmpeg/ /usr/local/
ENV PATH="/usr/local/bin:${PATH}"
ENV LD_LIBRARY_PATH="/usr/local/lib:${LD_LIBRARY_PATH}"

WORKDIR /app
COPY package*.json ./
RUN npm install --omit=dev
COPY . .

ENV NODE_ENV=production
ENV PORT=8080
EXPOSE 8080
ENTRYPOINT ["node", "server.js"]
