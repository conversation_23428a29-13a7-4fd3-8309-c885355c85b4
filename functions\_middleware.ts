export async function onRequest(context: any) {
  const { request, env } = context;
  // Lightweight pre-check for oversized uploads using Content-Length header
  if (request.method === 'POST') {
    const url = new URL(request.url);
    if (url.pathname.startsWith('/api/upload')) {
      const maxMb = Number(env.MAX_UPLOAD_MB || 200);
      const lenHeader = request.headers.get('content-length');
      if (lenHeader) {
        const bytes = Number(lenHeader);
        if (Number.isFinite(bytes) && bytes > maxMb * 1024 * 1024) {
          return new Response(JSON.stringify({ error: 'payload_too_large', maxMb }), { status: 413, headers: { 'content-type': 'application/json' } });
        }
      }
    }
  }
  const res = await context.next();
  const r = new Response(res.body, res);

  r.headers.set("X-Content-Type-Options", "nosniff");
  r.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  r.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=(), interest-cohort=()");
  r.headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");

  // Inline scripts & eval currently needed for single-file app (Tailwind CDN, etc.).
  r.headers.set(
    "Content-Security-Policy",
    [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://unpkg.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com data:",
      "img-src 'self' data: blob:",
      "media-src 'self' blob: data:",
      // allow email API and oauth providers if needed
      "connect-src 'self' https://api.resend.com https://oauth2.googleapis.com https://accounts.google.com https://openidconnect.googleapis.com",
      "object-src 'none'",
      "frame-ancestors 'none'",
    ].join("; ")
  );

  return r;
}


