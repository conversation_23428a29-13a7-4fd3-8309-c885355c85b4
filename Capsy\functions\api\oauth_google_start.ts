function base64UrlEncode(bytes: ArrayBuffer): string {
  const bin = String.fromCharCode(...new Uint8Array(bytes));
  return btoa(bin).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

async function sha256ToBase64Url(input: string): Promise<string> {
  const enc = new TextEncoder();
  const data = enc.encode(input);
  const hash = await crypto.subtle.digest('SHA-256', data);
  return base64UrlEncode(hash);
}

export async function onRequestGet(ctx: any) {
  const { request, env } = ctx;
  const clientId = env.GOOGLE_CLIENT_ID as string | undefined;
  const clientSecret = env.GOOGLE_CLIENT_SECRET as string | undefined;
  if (!clientId || !clientSecret) {
    return new Response('Google OAuth not configured', { status: 500 });
  }

  const url = new URL(request.url);
  const origin = `${url.protocol}//${url.host}`;
  const redirectUri = `${origin}/api/oauth_google_callback`;

  const state = crypto.randomUUID();
  const codeVerifier = base64UrlEncode(crypto.getRandomValues(new Uint8Array(32)).buffer);
  const codeChallenge = await sha256ToBase64Url(codeVerifier);

  // Store state with codeVerifier, expires in 10 minutes
  await env.SESSIONS.put(`oauth:state:${state}`, JSON.stringify({ codeVerifier, redirectUri }), { expirationTtl: 600 });

  const authURL = new URL('https://accounts.google.com/o/oauth2/v2/auth');
  authURL.searchParams.set('client_id', clientId);
  authURL.searchParams.set('redirect_uri', redirectUri);
  authURL.searchParams.set('response_type', 'code');
  authURL.searchParams.set('scope', 'openid email profile');
  authURL.searchParams.set('state', state);
  authURL.searchParams.set('code_challenge', codeChallenge);
  authURL.searchParams.set('code_challenge_method', 'S256');
  authURL.searchParams.set('access_type', 'online');
  authURL.searchParams.set('include_granted_scopes', 'true');

  return new Response(null, { status: 302, headers: { Location: authURL.toString() } });
}


