function getCookie(name: string, cookie: string | null) {
  const m = cookie?.match(new RegExp(`(?:^|; )${name}=([^;]+)`));
  return m ? decodeURIComponent(m[1]) : null;
}

export async function onRequestGet(ctx: any) {
  const { request, env } = ctx;
  const url = new URL(request.url);
  const token = url.searchParams.get('token');
  if (!token) return new Response('Missing token', { status: 400 });

  const data = await env.SESSIONS.get(`magic:${token}`);
  if (!data) return new Response('Invalid or expired link', { status: 400 });

  let email: string;
  try { email = JSON.parse(data).email; } catch { return new Response('Invalid token', { status: 400 }); }
  if (!email) return new Response('Invalid token', { status: 400 });

  const db = env.DB as D1Database;
  let user = await db
    .prepare('SELECT id, email, provider FROM users WHERE email = ?')
    .bind(email)
    .first<{ id: number; email: string; provider: string }>();
  if (!user) {
    try { await db.prepare('INSERT INTO users (email, provider, verified) VALUES (?, ?, 1)').bind(email, 'email').run(); }
    catch(_) { await db.prepare('INSERT INTO users (email, provider) VALUES (?, ?)').bind(email, 'email').run(); }
    user = await db.prepare('SELECT id, email, provider FROM users WHERE email = ?').bind(email).first();
  } else {
    try { await db.prepare('UPDATE users SET verified = 1 WHERE id = ?').bind((user as any).id).run(); } catch(_) {}
  }

  // Create session cookie
  const sid = crypto.randomUUID();
  const ttl = 60 * 60 * 24 * 30; // 30 days
  const session = { userId: (user as any).id, email: (user as any).email, provider: (user as any).provider || 'email' };
  await env.SESSIONS.put(`sid:${sid}`, JSON.stringify(session), { expirationTtl: ttl });
  await env.SESSIONS.delete(`magic:${token}`);

  const cookie = [
    `sid=${sid}`,
    'Path=/',
    'HttpOnly',
    'Secure',
    'SameSite=Lax',
    `Max-Age=${ttl}`,
  ].join('; ');

  // Redirect back to app root with cookie set
  return new Response(null, { status: 302, headers: { Location: '/', 'set-cookie': cookie } });
}


